# Shopee FCM Token Sender

This project allows you to send FCM tokens to <PERSON><PERSON>'s server using the same protocol as the official app, based on reverse engineering of the Android application.

## Files Overview

- `shopee_fcm_sender.js` - Main Node.js script to send FCM token to Shopee server
- `frida_capture_shopee_data.js` - Frida script to capture user and device data
- `frida_network_capture.js` - Frida script to capture network communication details
- `update_config.js` - Helper script to update the sender with captured data

## Prerequisites

1. **Node.js** - Install from https://nodejs.org/
2. **Frida** - Install with `pip install frida-tools`
3. **Android device** with Shopee app installed
4. **USB debugging** enabled on Android device

## Step-by-Step Instructions

### Step 1: Install Dependencies

```bash
npm install
```

### Step 2: Capture Data with Frida

1. Connect your Android device via USB
2. Make sure Shopee app is installed but not running
3. Run the Frida script to capture user and device data:

```bash
frida -U -f com.shopee.vn -l frida_capture_shopee_data.js --no-pause
```

4. In the Shopee app:
   - Log in to your account
   - Navigate through the app to trigger FCM token refresh
   - Wait for the Frida script to capture data

5. In the Frida console, export the captured data:
```javascript
exportShopeeData()
```

6. Copy the JSON output and save it to a file (e.g., `captured_data.json`)

### Step 3: Capture Network Details (Optional)

For more detailed network information, run the network capture script:

```bash
frida -U -f com.shopee.vn -l frida_network_capture.js --no-pause
```

Then in Frida console:
```javascript
exportNetworkData()
```

### Step 4: Update Configuration

Update the Node.js sender with the captured data:

```bash
node update_config.js captured_data.json
```

### Step 5: Send FCM Token

Run the sender to register your FCM token with Shopee's server:

```bash
node shopee_fcm_sender.js
```

## Manual Configuration

If you prefer to manually update the configuration, edit `shopee_fcm_sender.js` and update these sections:

### Server Configuration
```javascript
this.serverHost = 'your.server.host'; // From network capture
this.serverPort = 20443; // Usually 20443 for SSL or 20080 for non-SSL
```

### User Information
```javascript
this.userInfo = {
    userId: 123456789, // From loggedInUser.getUserId()
    tocUserId: 987654321, // From loggedInUser.getTocUserId()
    token: 'user_session_token', // From loggedInUser.getToken()
    username: 'your_username', // From loggedInUser.getUsername()
    country: 'VN',
    language: 'vi'
};
```

### Device Information
```javascript
this.deviceInfo = {
    deviceId: 'base64_device_id', // From deviceStore.k()
    deviceFingerprint: 'device_fingerprint', // From deviceStore.m()
    clientId: 'client_id', // From deviceStore.g()
    userAgent: 'user_agent_string', // From k.m().c()
    machineCode: 'android_gcm', // Usually 'android_gcm' or 'android_gpns'
    appVersion: 32300,
    isRooted: false
};
```

### FCM Token
```javascript
this.fcmToken = 'your_fcm_token_here'; // From Firebase
```

## Troubleshooting

### Common Issues

1. **"Configuration errors"** - Make sure all required fields are captured from Frida
2. **"Connection timeout"** - Check if server host/port are correct
3. **"Connection refused"** - Server might be blocking connections or using different ports

### Debug Mode

Enable debug logging by modifying the sender:

```javascript
// Add this at the top of shopee_fcm_sender.js
const DEBUG = true;

// Then add debug logs throughout the code
if (DEBUG) console.log('Debug info:', data);
```

### Frida Troubleshooting

1. **App crashes** - Try running without `--no-pause` flag
2. **No data captured** - Make sure you're logged in and using the app actively
3. **Permission denied** - Check USB debugging is enabled

## Important Notes

⚠️ **Legal Disclaimer**: This is for educational and research purposes only. Make sure you comply with Shopee's Terms of Service and applicable laws.

⚠️ **Security**: Never share your captured tokens or user data. Keep all configuration files secure.

⚠️ **Rate Limiting**: Don't spam the server with requests. Use reasonable delays between requests.

## Protocol Details

The implementation is based on reverse engineering of Shopee's Android app:

- **SetUserInfo** (Command 67) - Used for logged-in users
- **RegisterDevice** (Command 158) - Used for device registration
- **TCP Protocol** - Custom TCP protocol over SSL (port 20443)
- **Protobuf** - Uses Protocol Buffers for message serialization

## Contributing

If you find issues or improvements:

1. Test thoroughly on your setup
2. Document any changes
3. Consider security implications
4. Share findings responsibly

## License

This project is for educational purposes only. Use at your own risk.
