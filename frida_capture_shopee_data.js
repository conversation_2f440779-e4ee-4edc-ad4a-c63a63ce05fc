// Frida script to capture Shopee FCM and user data
// Usage: frida -U -f com.shopee.vn -l frida_capture_shopee_data.js --no-pause

Java.perform(function () {
    console.log("[*] Starting Shopee data capture...");

    var capturedData = {
        serverHost: null,
        serverPort: null,
        userInfo: {},
        deviceInfo: {},
        fcmToken: null,
        networkRequests: []
    };

    // Hook ShopeeApplication to get component access
    try {
        var ShopeeApplication = Java.use("com.shopee.app.application.ShopeeApplication");
        console.log("[+] Found ShopeeApplication class");

        // Hook get() method to capture application instance
        ShopeeApplication.get.implementation = function () {
            var result = this.get();
            console.log("[+] ShopeeApplication.get() called");

            try {
                // Get user info
                var component = result.getComponent();
                var loggedInUser = component.loggedInUser();

                if (loggedInUser.isLoggedIn()) {
                    capturedData.userInfo = {
                        userId: loggedInUser.getUserId(),
                        tocUserId: loggedInUser.getTocUserId(),
                        token: loggedInUser.getToken(),
                        username: loggedInUser.getUsername(),
                        email: loggedInUser.getEmail(),
                        country: loggedInUser.getCountry(),
                        shopId: loggedInUser.getShopId()
                    };
                    console.log("[+] Captured user info:", JSON.stringify(capturedData.userInfo, null, 2));
                }

                // Get device info
                var deviceStore = component.deviceStore();
                capturedData.deviceInfo = {
                    deviceId: deviceStore.k(), // Base64 encoded device ID
                    deviceFingerprint: Java.cast(deviceStore.m(), Java.use("[B")), // byte array
                    clientId: deviceStore.g(),
                    userAgent: null, // Will capture from k.m().c()
                    machineCode: deviceStore.s(),
                    isRooted: false // Will capture from q1.d()
                };

                // Convert byte array to string for logging
                var fingerprintBytes = Java.cast(deviceStore.m(), Java.use("[B"));
                var fingerprintStr = "";
                for (var i = 0; i < fingerprintBytes.length; i++) {
                    fingerprintStr += String.fromCharCode(fingerprintBytes[i] & 0xFF);
                }
                capturedData.deviceInfo.deviceFingerprintStr = fingerprintStr;

                console.log("[+] Captured device info:", JSON.stringify({
                    deviceId: capturedData.deviceInfo.deviceId.substring(0, 20) + "...",
                    clientId: capturedData.deviceInfo.clientId,
                    machineCode: capturedData.deviceInfo.machineCode
                }, null, 2));

            } catch (e) {
                console.log("[-] Error capturing component data:", e);
            }

            return result;
        };

    } catch (e) {
        console.log("[-] Error hooking ShopeeApplication:", e);
    }

    // Hook FCM token reception
    try {
        var ShopeeFcmMessageService = Java.use("com.shopee.app.pushnotification.fcm.ShopeeFcmMessageService");
        console.log("[+] Found ShopeeFcmMessageService class");

        ShopeeFcmMessageService.onNewToken.implementation = function (token) {
            console.log("[+] FCM Token received:", token);
            capturedData.fcmToken = token;

            // Call original method
            this.onNewToken(token);
        };

    } catch (e) {
        console.log("[-] Error hooking ShopeeFcmMessageService:", e);
    }

    // Hook network manager to capture server details
    try {
        var NetworkManager = Java.use("com.shopee.app.network.e");
        console.log("[+] Found NetworkManager class");

        // Hook connection creation
        NetworkManager.b.implementation = function () {
            var result = this.b();
            console.log("[+] Network connection created");

            try {
                // Try to get server host from connection
                var serverEndpoint = this.c.e();
                console.log("[+] Server endpoint:", serverEndpoint);
                capturedData.serverHost = serverEndpoint.split(":")[0];
                if (serverEndpoint.includes(":")) {
                    capturedData.serverPort = parseInt(serverEndpoint.split(":")[1]);
                }
            } catch (e) {
                console.log("[-] Error getting server endpoint:", e);
            }

            return result;
        };

    } catch (e) {
        console.log("[-] Error hooking NetworkManager:", e);
    }

    // Hook SetUserInfo request creation
    try {
        var SetUserInfoRequest = Java.use("com.shopee.app.network.request.z");
        console.log("[+] Found SetUserInfo request class");

        SetUserInfoRequest.h.implementation = function (deviceId, language, fingerprint, userAgent, isTempered, fingerprintBeforeTemper, fcmToken, machineCode) {
            console.log("[+] SetUserInfo request parameters:");
            console.log("  - Device ID:", deviceId ? deviceId.substring(0, 20) + "..." : "null");
            console.log("  - Language:", language);
            console.log("  - User Agent:", userAgent);
            console.log("  - FCM Token:", fcmToken ? fcmToken.substring(0, 20) + "..." : "null");
            console.log("  - Machine Code:", machineCode);

            // Update captured data
            capturedData.deviceInfo.userAgent = userAgent;
            capturedData.deviceInfo.language = language;
            if (fcmToken) capturedData.fcmToken = fcmToken;

            // Call original method
            this.h(deviceId, language, fingerprint, userAgent, isTempered, fingerprintBeforeTemper, fcmToken, machineCode);
        };

    } catch (e) {
        console.log("[-] Error hooking SetUserInfo request:", e);
    }

    // Hook user agent utility
    try {
        var UserAgentUtil = Java.use("com.shopee.app.util.k");
        console.log("[+] Found UserAgent utility class");

        var userAgentInstance = UserAgentUtil.m();
        if (userAgentInstance) {
            var userAgent = userAgentInstance.c();
            capturedData.deviceInfo.userAgent = userAgent;
            console.log("[+] Captured User Agent:", userAgent);
        }

    } catch (e) {
        console.log("[-] Error hooking UserAgent utility:", e);
    }

    // Hook root detection
    try {
        var RootDetection = Java.use("com.shopee.app.util.q1");
        console.log("[+] Found root detection class");

        RootDetection.d.implementation = function () {
            var result = this.d();
            capturedData.deviceInfo.isRooted = result;
            console.log("[+] Root detection result:", result);
            return result;
        };

    } catch (e) {
        console.log("[-] Error hooking root detection:", e);
    }

    // Hook TCP packet sending to capture server communication
    try {
        var TCPPacket = Java.use("com.beetalklib.network.tcp.f");
        console.log("[+] Found TCP packet class");

        // This might help capture the actual network communication

    } catch (e) {
        console.log("[-] Error hooking TCP packet:", e);
    }

    // Periodically output captured data
    setInterval(function () {
        if (Object.keys(capturedData.userInfo).length > 0 || capturedData.fcmToken) {
            console.log("\n[*] === CAPTURED DATA SUMMARY ===");
            console.log(JSON.stringify(capturedData, null, 2));
            console.log("[*] ================================\n");
        }
    }, 10000); // Every 10 seconds

    // Export data function
    function exportCapturedData() {
        console.log("\n[*] === FINAL CAPTURED DATA ===");
        console.log("Copy this JSON to update your Node.js script:");
        console.log(JSON.stringify(capturedData, null, 2));
        console.log("[*] ===============================\n");
        return capturedData;
    }

    // Make export function available in Frida's global scope
    rpc.exports.exportShopeeData = exportCapturedData;

    // Also make it available as a global function for direct calling
    this.exportShopeeData = exportCapturedData;

    console.log("[*] Frida hooks installed. Trigger FCM token refresh or login to capture data.");
    console.log("[*] Call exportShopeeData() in Frida console to get final data.");
});
