package androidx.fragment;

/* loaded from: classes.dex */
public final class R {

    public static final class anim {
        public static final int fragment_fast_out_extra_slow_in = 0x7f010034;

        private anim() {
        }
    }

    public static final class animator {
        public static final int fragment_close_enter = 0x7f020003;
        public static final int fragment_close_exit = 0x7f020004;
        public static final int fragment_fade_enter = 0x7f020005;
        public static final int fragment_fade_exit = 0x7f020006;
        public static final int fragment_open_enter = 0x7f020007;
        public static final int fragment_open_exit = 0x7f020008;

        private animator() {
        }
    }

    public static final class attr {
        public static final int alpha = 0x7f040043;
        public static final int font = 0x7f0402ba;
        public static final int fontProviderAuthority = 0x7f0402bc;
        public static final int fontProviderCerts = 0x7f0402bd;
        public static final int fontProviderFetchStrategy = 0x7f0402be;
        public static final int fontProviderFetchTimeout = 0x7f0402bf;
        public static final int fontProviderPackage = 0x7f0402c0;
        public static final int fontProviderQuery = 0x7f0402c1;
        public static final int fontStyle = 0x7f0402c3;
        public static final int fontVariationSettings = 0x7f0402c4;
        public static final int fontWeight = 0x7f0402c5;
        public static final int ttcIndex = 0x7f040943;

        private attr() {
        }
    }

    public static final class color {
        public static final int notification_action_color_filter = 0x7f060294;
        public static final int notification_icon_bg_color = 0x7f060295;
        public static final int ripple_material_light = 0x7f060475;
        public static final int secondary_text_default_material_light = 0x7f06049c;

        private color() {
        }
    }

    public static final class dimen {
        public static final int compat_button_inset_horizontal_material = 0x7f0700a8;
        public static final int compat_button_inset_vertical_material = 0x7f0700a9;
        public static final int compat_button_padding_horizontal_material = 0x7f0700aa;
        public static final int compat_button_padding_vertical_material = 0x7f0700ab;
        public static final int compat_control_corner_material = 0x7f0700ac;
        public static final int compat_notification_large_icon_max_height = 0x7f0700ad;
        public static final int compat_notification_large_icon_max_width = 0x7f0700ae;
        public static final int notification_action_icon_size = 0x7f07026d;
        public static final int notification_action_text_size = 0x7f07026e;
        public static final int notification_big_circle_margin = 0x7f07026f;
        public static final int notification_content_margin_start = 0x7f070270;
        public static final int notification_large_icon_height = 0x7f070271;
        public static final int notification_large_icon_width = 0x7f070272;
        public static final int notification_main_column_padding_top = 0x7f070273;
        public static final int notification_media_narrow_margin = 0x7f070274;
        public static final int notification_right_icon_size = 0x7f070275;
        public static final int notification_right_side_padding_top = 0x7f070276;
        public static final int notification_small_icon_background_padding = 0x7f070277;
        public static final int notification_small_icon_size_as_large = 0x7f070278;
        public static final int notification_subtext_size = 0x7f070279;
        public static final int notification_top_pad = 0x7f07027a;
        public static final int notification_top_pad_large_text = 0x7f07027b;

        private dimen() {
        }
    }

    public static final class drawable {
        public static final int notification_action_background = 0x7f080599;
        public static final int notification_bg = 0x7f08059a;
        public static final int notification_bg_low = 0x7f08059b;
        public static final int notification_bg_low_normal = 0x7f08059c;
        public static final int notification_bg_low_pressed = 0x7f08059d;
        public static final int notification_bg_normal = 0x7f08059e;
        public static final int notification_bg_normal_pressed = 0x7f08059f;
        public static final int notification_icon_background = 0x7f0805a0;
        public static final int notification_template_icon_bg = 0x7f0805a1;
        public static final int notification_template_icon_low_bg = 0x7f0805a2;
        public static final int notification_tile_bg = 0x7f0805a3;
        public static final int notify_panel_notification_icon_bg = 0x7f0805a4;

        private drawable() {
        }
    }

    public static final class id {
        public static final int accessibility_action_clickable_span = 0x7f090035;
        public static final int accessibility_custom_action_0 = 0x7f090037;
        public static final int accessibility_custom_action_1 = 0x7f090038;
        public static final int accessibility_custom_action_10 = 0x7f090039;
        public static final int accessibility_custom_action_11 = 0x7f09003a;
        public static final int accessibility_custom_action_12 = 0x7f09003b;
        public static final int accessibility_custom_action_13 = 0x7f09003c;
        public static final int accessibility_custom_action_14 = 0x7f09003d;
        public static final int accessibility_custom_action_15 = 0x7f09003e;
        public static final int accessibility_custom_action_16 = 0x7f09003f;
        public static final int accessibility_custom_action_17 = 0x7f090040;
        public static final int accessibility_custom_action_18 = 0x7f090041;
        public static final int accessibility_custom_action_19 = 0x7f090042;
        public static final int accessibility_custom_action_2 = 0x7f090043;
        public static final int accessibility_custom_action_20 = 0x7f090044;
        public static final int accessibility_custom_action_21 = 0x7f090045;
        public static final int accessibility_custom_action_22 = 0x7f090046;
        public static final int accessibility_custom_action_23 = 0x7f090047;
        public static final int accessibility_custom_action_24 = 0x7f090048;
        public static final int accessibility_custom_action_25 = 0x7f090049;
        public static final int accessibility_custom_action_26 = 0x7f09004a;
        public static final int accessibility_custom_action_27 = 0x7f09004b;
        public static final int accessibility_custom_action_28 = 0x7f09004c;
        public static final int accessibility_custom_action_29 = 0x7f09004d;
        public static final int accessibility_custom_action_3 = 0x7f09004e;
        public static final int accessibility_custom_action_30 = 0x7f09004f;
        public static final int accessibility_custom_action_31 = 0x7f090050;
        public static final int accessibility_custom_action_4 = 0x7f090051;
        public static final int accessibility_custom_action_5 = 0x7f090052;
        public static final int accessibility_custom_action_6 = 0x7f090053;
        public static final int accessibility_custom_action_7 = 0x7f090054;
        public static final int accessibility_custom_action_8 = 0x7f090055;
        public static final int accessibility_custom_action_9 = 0x7f090056;
        public static final int action_container = 0x7f090069;
        public static final int action_divider = 0x7f09006b;
        public static final int action_image = 0x7f09006d;
        public static final int action_text = 0x7f090078;
        public static final int actions = 0x7f090082;
        public static final int async = 0x7f0900d7;
        public static final int blocking = 0x7f09011c;
        public static final int chronometer = 0x7f09029d;
        public static final int dialog_button = 0x7f09036b;
        public static final int forever = 0x7f090478;
        public static final int fragment_container_view_tag = 0x7f09047f;
        public static final int icon = 0x7f0904ec;
        public static final int icon_group = 0x7f0904f2;

        /* renamed from: info, reason: collision with root package name */
        public static final int f19info = 0x7f09053a;
        public static final int italic = 0x7f090551;
        public static final int line1 = 0x7f0905f5;
        public static final int line3 = 0x7f0905f6;
        public static final int normal = 0x7f090729;
        public static final int notification_background = 0x7f09073d;
        public static final int notification_main_column = 0x7f09073e;
        public static final int notification_main_column_container = 0x7f09073f;
        public static final int right_icon = 0x7f0908a1;
        public static final int right_side = 0x7f0908a5;
        public static final int special_effects_controller_view_tag = 0x7f090a12;
        public static final int tag_accessibility_actions = 0x7f090a7b;
        public static final int tag_accessibility_clickable_spans = 0x7f090a7c;
        public static final int tag_accessibility_heading = 0x7f090a7d;
        public static final int tag_accessibility_pane_title = 0x7f090a7e;
        public static final int tag_screen_reader_focusable = 0x7f090a84;
        public static final int tag_transition_group = 0x7f090a86;
        public static final int tag_unhandled_key_event_manager = 0x7f090a87;
        public static final int tag_unhandled_key_listeners = 0x7f090a88;
        public static final int text = 0x7f090aa1;
        public static final int text2 = 0x7f090aa3;
        public static final int time = 0x7f090ae4;
        public static final int title = 0x7f090af7;
        public static final int view_tree_lifecycle_owner = 0x7f090c8d;
        public static final int view_tree_saved_state_registry_owner = 0x7f090c8e;
        public static final int view_tree_view_model_store_owner = 0x7f090c8f;
        public static final int visible_removing_fragment_view_tag = 0x7f090c9a;

        private id() {
        }
    }

    public static final class integer {
        public static final int status_bar_notification_info_maxnum = 0x7f0a003f;

        private integer() {
        }
    }

    public static final class layout {
        public static final int custom_dialog = 0x7f0c00b8;
        public static final int notification_action = 0x7f0c0280;
        public static final int notification_action_tombstone = 0x7f0c0281;
        public static final int notification_template_custom_big = 0x7f0c028c;
        public static final int notification_template_icon_group = 0x7f0c028d;
        public static final int notification_template_part_chronometer = 0x7f0c0291;
        public static final int notification_template_part_time = 0x7f0c0292;

        private layout() {
        }
    }

    public static final class string {
        public static final int status_bar_notification_info_overflow = 0x7f111e9c;

        private string() {
        }
    }

    public static final class style {
        public static final int TextAppearance_Compat_Notification = 0x7f1202cb;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f1202cc;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f1202ce;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f1202d1;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f1202d3;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f1203de;
        public static final int Widget_Compat_NotificationActionText = 0x7f1203df;

        private style() {
        }
    }

    public static final class styleable {
        public static final int ColorStateListItem_alpha = 0x00000002;
        public static final int ColorStateListItem_android_alpha = 0x00000001;
        public static final int ColorStateListItem_android_color = 0x00000000;
        public static final int FontFamilyFont_android_font = 0x00000000;
        public static final int FontFamilyFont_android_fontStyle = 0x00000002;
        public static final int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static final int FontFamilyFont_android_fontWeight = 0x00000001;
        public static final int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static final int FontFamilyFont_font = 0x00000005;
        public static final int FontFamilyFont_fontStyle = 0x00000006;
        public static final int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static final int FontFamilyFont_fontWeight = 0x00000008;
        public static final int FontFamilyFont_ttcIndex = 0x00000009;
        public static final int FontFamily_fontProviderAuthority = 0x00000000;
        public static final int FontFamily_fontProviderCerts = 0x00000001;
        public static final int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static final int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static final int FontFamily_fontProviderPackage = 0x00000004;
        public static final int FontFamily_fontProviderQuery = 0x00000005;
        public static final int FontFamily_fontProviderSystemFontFamily = 0x00000006;
        public static final int FragmentContainerView_android_name = 0x00000000;
        public static final int FragmentContainerView_android_tag = 0x00000001;
        public static final int Fragment_android_id = 0x00000001;
        public static final int Fragment_android_name = 0x00000000;
        public static final int Fragment_android_tag = 0x00000002;
        public static final int GradientColorItem_android_color = 0x00000000;
        public static final int GradientColorItem_android_offset = 0x00000001;
        public static final int GradientColor_android_centerColor = 0x00000007;
        public static final int GradientColor_android_centerX = 0x00000003;
        public static final int GradientColor_android_centerY = 0x00000004;
        public static final int GradientColor_android_endColor = 0x00000001;
        public static final int GradientColor_android_endX = 0x0000000a;
        public static final int GradientColor_android_endY = 0x0000000b;
        public static final int GradientColor_android_gradientRadius = 0x00000005;
        public static final int GradientColor_android_startColor = 0x00000000;
        public static final int GradientColor_android_startX = 0x00000008;
        public static final int GradientColor_android_startY = 0x00000009;
        public static final int GradientColor_android_tileMode = 0x00000006;
        public static final int GradientColor_android_type = 0x00000002;
        public static final int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, com.shopeepay.merchant.vn.R.attr.alpha};
        public static final int[] FontFamily = {com.shopeepay.merchant.vn.R.attr.fontProviderAuthority, com.shopeepay.merchant.vn.R.attr.fontProviderCerts, com.shopeepay.merchant.vn.R.attr.fontProviderFetchStrategy, com.shopeepay.merchant.vn.R.attr.fontProviderFetchTimeout, com.shopeepay.merchant.vn.R.attr.fontProviderPackage, com.shopeepay.merchant.vn.R.attr.fontProviderQuery, com.shopeepay.merchant.vn.R.attr.fontProviderSystemFontFamily};
        public static final int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, com.shopeepay.merchant.vn.R.attr.font, com.shopeepay.merchant.vn.R.attr.fontStyle, com.shopeepay.merchant.vn.R.attr.fontVariationSettings, com.shopeepay.merchant.vn.R.attr.fontWeight, com.shopeepay.merchant.vn.R.attr.ttcIndex};
        public static final int[] Fragment = {android.R.attr.name, android.R.attr.id, android.R.attr.tag};
        public static final int[] FragmentContainerView = {android.R.attr.name, android.R.attr.tag};
        public static final int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static final int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};

        private styleable() {
        }
    }

    private R() {
    }
}
