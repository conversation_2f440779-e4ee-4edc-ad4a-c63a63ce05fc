// Simplified <PERSON><PERSON> script to capture Shopee FCM and user data
// Usage: frida -U -f com.shopee.vn -l frida_simple_capture.js --no-pause

console.log("[*] Starting simplified Shopee data capture...");

var capturedData = {
    serverHost: null,
    serverPort: null,
    userInfo: {},
    deviceInfo: {},
    fcmToken: null
};

Java.perform(function() {
    
    // Hook FCM token reception - this is the most important
    try {
        var ShopeeFcmMessageService = Java.use("com.shopee.app.pushnotification.fcm.ShopeeFcmMessageService");
        console.log("[+] Found ShopeeFcmMessageService class");
        
        ShopeeFcmMessageService.onNewToken.implementation = function(token) {
            console.log("[+] *** FCM Token captured: " + token.substring(0, 30) + "...");
            capturedData.fcmToken = token;
            
            // Call original method
            this.onNewToken(token);
        };
        
    } catch (e) {
        console.log("[-] Error hooking <PERSON>eeFcmMessageService:", e);
    }
    
    // Hook SetUserInfo request to capture parameters
    try {
        var SetUserInfoRequest = Java.use("com.shopee.app.network.request.z");
        console.log("[+] Found SetUserInfo request class");
        
        SetUserInfoRequest.h.implementation = function(deviceId, language, fingerprint, userAgent, isTempered, fingerprintBeforeTemper, fcmToken, machineCode) {
            console.log("[+] *** SetUserInfo request captured:");
            console.log("  - Device ID: " + (deviceId ? deviceId.substring(0, 20) + "..." : "null"));
            console.log("  - Language: " + language);
            console.log("  - User Agent: " + userAgent);
            console.log("  - FCM Token: " + (fcmToken ? fcmToken.substring(0, 20) + "..." : "null"));
            console.log("  - Machine Code: " + machineCode);
            
            // Update captured data
            capturedData.deviceInfo.deviceId = deviceId;
            capturedData.deviceInfo.language = language;
            capturedData.deviceInfo.userAgent = userAgent;
            capturedData.deviceInfo.machineCode = machineCode;
            if (fcmToken) capturedData.fcmToken = fcmToken;
            
            // Call original method
            this.h(deviceId, language, fingerprint, userAgent, isTempered, fingerprintBeforeTemper, fcmToken, machineCode);
        };
        
    } catch (e) {
        console.log("[-] Error hooking SetUserInfo request:", e);
    }
    
    // Hook device store methods
    try {
        var DeviceStore = Java.use("com.shopee.app.data.store.j0");
        console.log("[+] Found DeviceStore class");
        
        // Hook device ID getter
        DeviceStore.k.implementation = function() {
            var result = this.k();
            if (result && !capturedData.deviceInfo.deviceId) {
                console.log("[+] *** Device ID captured: " + result.substring(0, 20) + "...");
                capturedData.deviceInfo.deviceId = result;
            }
            return result;
        };
        
        // Hook client ID getter
        DeviceStore.g.implementation = function() {
            var result = this.g();
            if (result && !capturedData.deviceInfo.clientId) {
                console.log("[+] *** Client ID captured: " + result);
                capturedData.deviceInfo.clientId = result;
            }
            return result;
        };
        
        // Hook machine code getter
        DeviceStore.s.implementation = function() {
            var result = this.s();
            if (result && !capturedData.deviceInfo.machineCode) {
                console.log("[+] *** Machine Code captured: " + result);
                capturedData.deviceInfo.machineCode = result;
            }
            return result;
        };
        
    } catch (e) {
        console.log("[-] Error hooking DeviceStore:", e);
    }
    
    // Hook UserInfo methods
    try {
        var UserInfo = Java.use("com.shopee.app.appuser.UserInfo");
        console.log("[+] Found UserInfo class");
        
        UserInfo.getUserId.implementation = function() {
            var result = this.getUserId();
            if (result && !capturedData.userInfo.userId) {
                console.log("[+] *** User ID captured: " + result);
                capturedData.userInfo.userId = result;
            }
            return result;
        };
        
        UserInfo.getToken.implementation = function() {
            var result = this.getToken();
            if (result && !capturedData.userInfo.token) {
                console.log("[+] *** User Token captured: " + result.substring(0, 20) + "...");
                capturedData.userInfo.token = result;
            }
            return result;
        };
        
        UserInfo.getUsername.implementation = function() {
            var result = this.getUsername();
            if (result && !capturedData.userInfo.username) {
                console.log("[+] *** Username captured: " + result);
                capturedData.userInfo.username = result;
            }
            return result;
        };
        
        UserInfo.getTocUserId.implementation = function() {
            var result = this.getTocUserId();
            if (result && !capturedData.userInfo.tocUserId) {
                console.log("[+] *** TOC User ID captured: " + result);
                capturedData.userInfo.tocUserId = result;
            }
            return result;
        };
        
    } catch (e) {
        console.log("[-] Error hooking UserInfo:", e);
    }
    
    // Hook socket connections to capture server details
    try {
        var Socket = Java.use("java.net.Socket");
        console.log("[+] Found Socket class");
        
        Socket.$init.overload('java.lang.String', 'int').implementation = function(host, port) {
            console.log("[+] *** Socket connection to: " + host + ":" + port);
            if (!capturedData.serverHost) {
                capturedData.serverHost = host;
                capturedData.serverPort = port;
            }
            return this.$init(host, port);
        };
        
    } catch (e) {
        console.log("[-] Error hooking Socket:", e);
    }
    
    // Periodically show captured data
    setInterval(function() {
        var hasData = capturedData.fcmToken || 
                     Object.keys(capturedData.userInfo).length > 0 || 
                     Object.keys(capturedData.deviceInfo).length > 0;
        
        if (hasData) {
            console.log("\n[*] === CURRENT CAPTURED DATA ===");
            console.log("FCM Token: " + (capturedData.fcmToken ? "✓ Captured" : "✗ Missing"));
            console.log("User Info: " + Object.keys(capturedData.userInfo).length + " fields");
            console.log("Device Info: " + Object.keys(capturedData.deviceInfo).length + " fields");
            console.log("Server: " + (capturedData.serverHost ? capturedData.serverHost + ":" + capturedData.serverPort : "Not captured"));
            console.log("[*] ===============================\n");
        }
    }, 15000);
    
    console.log("[*] Simplified hooks installed successfully!");
    console.log("[*] Now:");
    console.log("[*] 1. Login to Shopee app");
    console.log("[*] 2. Navigate through the app");
    console.log("[*] 3. Wait for data to be captured");
    console.log("[*] 4. Call exportData() to get the JSON");
});

// Simple export function that works
function exportData() {
    console.log("\n[*] === FINAL CAPTURED DATA ===");
    console.log("Copy this JSON and save it to a file:");
    console.log("=====================================");
    console.log(JSON.stringify(capturedData, null, 2));
    console.log("=====================================");
    return capturedData;
}

// Make export available
rpc.exports.exportData = exportData;
