{"name": "shopee-fcm-sender", "version": "1.0.0", "description": "Send FCM tokens to Shopee server using reverse-engineered protocol", "main": "shopee_fcm_sender.js", "scripts": {"start": "node shopee_fcm_sender.js", "capture": "echo 'Run: frida -U -f com.shopee.vn -l frida_capture_shopee_data.js --no-pause'", "update": "node update_config.js", "test": "node shopee_fcm_sender.js --validate-only"}, "keywords": ["shopee", "fcm", "firebase", "push-notification", "reverse-engineering", "frida"], "author": "Research Project", "license": "MIT", "dependencies": {"protobufjs": "^7.2.5"}, "devDependencies": {"eslint": "^8.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "local"}, "bugs": {"url": "local"}, "homepage": "local"}