// Frida script to capture Shopee network communication details
// Usage: frida -U -f com.shopee.vn -l frida_network_capture.js --no-pause

Java.perform(function () {
    console.log("[*] Starting Shopee network capture...");

    var networkData = {
        serverEndpoints: [],
        tcpConnections: [],
        setUserInfoRequests: [],
        registerDeviceRequests: []
    };

    // Hook network configuration
    try {
        var NetworkConfig = Java.use("com.shopee.app.util.n");
        console.log("[+] Found network config class");

        // Try to get server endpoints
        try {
            console.log("[+] Server domain:", NetworkConfig.c.value);
            console.log("[+] Server endpoint:", NetworkConfig.e.value);
            networkData.serverEndpoints.push({
                domain: NetworkConfig.c.value,
                endpoint: NetworkConfig.e.value
            });
        } catch (e) {
            console.log("[-] Error reading network config:", e);
        }

    } catch (e) {
        console.log("[-] Error hooking network config:", e);
    }

    // Hook TCP connection creation
    try {
        var NetworkManager = Java.use("com.shopee.app.network.e");
        console.log("[+] Found NetworkManager class");

        NetworkManager.b.implementation = function () {
            console.log("[+] Creating TCP connection...");
            var result = this.b();

            try {
                var endpoint = this.c.e();
                console.log("[+] TCP Connection endpoint:", endpoint);
                networkData.tcpConnections.push({
                    endpoint: endpoint,
                    timestamp: new Date().toISOString(),
                    ssl: this.a.sslEnabled()
                });
            } catch (e) {
                console.log("[-] Error capturing TCP connection details:", e);
            }

            return result;
        };

        // Hook packet sending
        NetworkManager.p.implementation = function (packet, commandStr, requestId, message) {
            console.log("[+] Sending TCP packet:");
            console.log("  - Command:", commandStr);
            console.log("  - Request ID:", requestId);
            console.log("  - Packet command:", packet.b());

            try {
                var packetData = packet.c();
                console.log("  - Packet size:", packetData.length);
                console.log("  - Packet hex (first 50 bytes):",
                    Java.cast(packetData, Java.use("[B")).slice(0, 50)
                        .map(b => (b & 0xFF).toString(16).padStart(2, '0')).join(' '));
            } catch (e) {
                console.log("[-] Error reading packet data:", e);
            }

            return this.p(packet, commandStr, requestId, message);
        };

    } catch (e) {
        console.log("[-] Error hooking NetworkManager:", e);
    }

    // Hook SetUserInfo request (for logged-in users)
    try {
        var SetUserInfoRequest = Java.use("com.shopee.app.network.request.z");
        console.log("[+] Found SetUserInfo request class");

        SetUserInfoRequest.d.implementation = function () {
            console.log("[+] Creating SetUserInfo TCP packet...");

            var result = this.d();

            try {
                console.log("  - Command:", result.b()); // Should be 67
                console.log("  - Payload size:", result.c().length);

                // Capture request details
                var requestData = {
                    command: result.b(),
                    timestamp: new Date().toISOString(),
                    deviceId: this.d ? this.d.substring(0, 20) + "..." : "unknown",
                    language: this.b || "unknown",
                    fcmToken: this.c ? this.c.substring(0, 20) + "..." : "unknown",
                    machineCode: this.i || "unknown"
                };

                networkData.setUserInfoRequests.push(requestData);
                console.log("[+] SetUserInfo request captured:", JSON.stringify(requestData, null, 2));

            } catch (e) {
                console.log("[-] Error capturing SetUserInfo details:", e);
            }

            return result;
        };

    } catch (e) {
        console.log("[-] Error hooking SetUserInfo request:", e);
    }

    // Hook RegisterDevice request (for non-logged-in users)
    try {
        var RegisterDeviceRequest = Java.use("com.shopee.app.network.request.login.k");
        console.log("[+] Found RegisterDevice request class");

        RegisterDeviceRequest.d.implementation = function () {
            console.log("[+] Creating RegisterDevice TCP packet...");

            var result = this.d();

            try {
                console.log("  - Command:", result.b()); // Should be 158
                console.log("  - Payload size:", result.c().length);

                // Capture request details
                var requestData = {
                    command: result.b(),
                    timestamp: new Date().toISOString(),
                    deviceId: this.b ? this.b.substring(0, 20) + "..." : "unknown",
                    fcmToken: this.c ? "present" : "missing",
                    machineCode: this.d || "unknown"
                };

                networkData.registerDeviceRequests.push(requestData);
                console.log("[+] RegisterDevice request captured:", JSON.stringify(requestData, null, 2));

            } catch (e) {
                console.log("[-] Error capturing RegisterDevice details:", e);
            }

            return result;
        };

    } catch (e) {
        console.log("[-] Error hooking RegisterDevice request:", e);
    }

    // Hook socket creation to capture actual server addresses
    try {
        var Socket = Java.use("java.net.Socket");
        console.log("[+] Found Socket class");

        Socket.$init.overload('java.lang.String', 'int').implementation = function (host, port) {
            console.log("[+] Socket connection to:", host + ":" + port);
            networkData.tcpConnections.push({
                host: host,
                port: port,
                timestamp: new Date().toISOString(),
                type: "socket"
            });

            return this.$init(host, port);
        };

    } catch (e) {
        console.log("[-] Error hooking Socket:", e);
    }

    // Hook SSL socket creation
    try {
        var SSLSocketFactory = Java.use("javax.net.ssl.SSLSocketFactory");
        console.log("[+] Found SSLSocketFactory class");

        SSLSocketFactory.createSocket.overload('java.lang.String', 'int').implementation = function (host, port) {
            console.log("[+] SSL Socket connection to:", host + ":" + port);
            networkData.tcpConnections.push({
                host: host,
                port: port,
                timestamp: new Date().toISOString(),
                type: "ssl_socket"
            });

            return this.createSocket(host, port);
        };

    } catch (e) {
        console.log("[-] Error hooking SSLSocketFactory:", e);
    }

    // Hook FCM message service to capture token updates
    try {
        var MessageServiceHandler = Java.use("com.shopee.app.pushnotification.b");
        console.log("[+] Found MessageServiceHandler class");

        MessageServiceHandler.h.implementation = function (token) {
            console.log("[+] FCM token being processed:", token ? token.substring(0, 30) + "..." : "null");

            // Call original method
            this.h(token);
        };

        MessageServiceHandler.e.implementation = function (token) {
            console.log("[+] Direct FCM registration with token:", token ? token.substring(0, 30) + "..." : "null");

            // Call original method
            this.e(token);
        };

    } catch (e) {
        console.log("[-] Error hooking MessageServiceHandler:", e);
    }

    // Periodically output network data
    setInterval(function () {
        if (networkData.tcpConnections.length > 0 || networkData.setUserInfoRequests.length > 0) {
            console.log("\n[*] === NETWORK DATA SUMMARY ===");
            console.log("TCP Connections:", networkData.tcpConnections.length);
            console.log("SetUserInfo Requests:", networkData.setUserInfoRequests.length);
            console.log("RegisterDevice Requests:", networkData.registerDeviceRequests.length);

            if (networkData.tcpConnections.length > 0) {
                var latestConnection = networkData.tcpConnections[networkData.tcpConnections.length - 1];
                console.log("Latest connection:", latestConnection.host + ":" + latestConnection.port);
            }

            console.log("[*] ==============================\n");
        }
    }, 15000); // Every 15 seconds

    // Export function
    function exportNetworkData() {
        console.log("\n[*] === NETWORK CAPTURE RESULTS ===");
        console.log(JSON.stringify(networkData, null, 2));
        console.log("[*] ==================================\n");
        return networkData;
    }

    // Make function available in Frida's global scope
    rpc.exports.exportNetworkData = exportNetworkData;

    // Store the function reference globally
    globalThis.exportNetworkData = exportNetworkData;

    console.log("[*] Network capture hooks installed.");
    console.log("[*] Trigger FCM registration or login to capture network data.");
    console.log("[*] Call exportNetworkData() to get captured network information.");
});
