<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="8dp"/>
            <solid android:color="@color/black12"/>
            <stroke
                android:width="1dp"
                android:color="@color/black08"/>
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="8dp"/>
            <solid android:color="@color/black12"/>
            <stroke
                android:width="1dp"
                android:color="@color/black08"/>
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="8dp"/>
            <solid android:color="@color/transparent"/>
            <stroke
                android:width="1px"
                android:color="@color/black26"/>
        </shape>
    </item>
</selector>
