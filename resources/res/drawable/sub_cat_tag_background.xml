<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="40dp"/>
            <solid android:color="@color/primary12"/>
            <stroke
                android:width="1dp"
                android:color="@color/primary"/>
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="40dp"/>
            <solid android:color="@color/primary12"/>
            <stroke
                android:width="1dp"
                android:color="@color/primary"/>
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="40dp"/>
            <solid android:color="@color/white"/>
            <stroke
                android:width="1dp"
                android:color="@color/black54"/>
        </shape>
    </item>
</selector>
