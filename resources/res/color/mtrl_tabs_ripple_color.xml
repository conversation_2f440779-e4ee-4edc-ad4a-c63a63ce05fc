<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_selected="true"
        android:state_pressed="true"
        android:color="?attr/colorPrimary"
        android:alpha="0.08"/>
    <item
        android:state_focused="true"
        android:state_selected="true"
        android:color="?attr/colorPrimary"
        android:alpha="0.16"
        android:state_hovered="true"/>
    <item
        android:state_focused="true"
        android:state_selected="true"
        android:color="?attr/colorPrimary"
        android:alpha="0.12"/>
    <item
        android:state_selected="true"
        android:color="?attr/colorPrimary"
        android:alpha="0.04"
        android:state_hovered="true"/>
    <item
        android:state_selected="true"
        android:color="?attr/colorPrimary"
        android:alpha="0"/>
    <item
        android:state_pressed="true"
        android:color="?attr/colorOnSurface"
        android:alpha="0.08"/>
    <item
        android:state_focused="true"
        android:color="?attr/colorOnSurface"
        android:alpha="0.16"
        android:state_hovered="true"/>
    <item
        android:state_focused="true"
        android:color="?attr/colorOnSurface"
        android:alpha="0.12"/>
    <item
        android:color="?attr/colorOnSurface"
        android:alpha="0.04"
        android:state_hovered="true"/>
    <item
        android:color="?attr/colorOnSurface"
        android:alpha="0"/>
</selector>
