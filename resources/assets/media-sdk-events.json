{"media_library_click_choose_library": {"page_id": "", "page_type": "media_library", "operation": "click", "page_section": "", "target_type": "choose_library", "data": ["business_id"]}, "media_library_impression_tab": {"page_id": "", "page_type": "media_library", "operation": "impression", "page_section": "", "target_type": "tab", "data": ["business_id", "tab_name"]}, "media_library_view": {"page_id": "", "page_type": "media_library", "operation": "view", "page_section": "", "target_type": "", "data": ["business_id", "library_type"]}, "media_library_click_media": {"page_id": "", "page_type": "media_library", "operation": "click", "page_section": "", "target_type": "media", "data": ["media_type", "business_id"]}, "media_library_click_select_media": {"page_id": "", "page_type": "media_library", "operation": "click", "page_section": "", "target_type": "select_media", "data": ["media_type", "business_id", "is_selected"]}, "media_library_click_next": {"page_id": "", "page_type": "media_library", "operation": "click", "page_section": "", "target_type": "next", "data": ["num_selected", "business_id"]}, "media_edit_view": {"page_id": "", "page_type": "media_edit", "operation": "view", "page_section": "", "target_type": "", "data": []}, "media_edit_click_tool": {"page_id": "", "page_type": "media_edit", "operation": "click", "page_section": "", "target_type": "edit_tool", "data": ["num_selected", "business_id"]}, "media_edit_current_view": {"page_id": "", "page_type": "media_edit", "operation": "impression", "page_section": "media_current_view", "target_type": "", "data": []}, "media_edit_click_back": {"page_id": "", "page_type": "media_edit", "operation": "click", "page_section": "", "target_type": "back", "data": []}, "media_library_click_selected_media_bar": {"page_id": "", "page_type": "media_library", "operation": "click", "page_section": "selected_media_bar", "target_type": "media", "data": []}, "media_library_click_selected_media_bar_unselect": {"page_id": "", "page_type": "media_library", "operation": "click", "page_section": "selected_media_bar", "target_type": "media_unselect", "data": []}, "media_library_click_exit": {"page_id": "", "page_type": "media_library", "operation": "click", "page_section": "", "target_type": "exit", "data": []}, "media_create_view": {"page_id": "", "page_type": "media_create", "operation": "view", "page_section": "", "target_type": "", "data": []}, "media_create_impression_mode_change": {"page_id": "", "page_type": "media_create", "operation": "impression", "page_section": "mode_change", "target_type": "", "data": []}, "media_create_click_change_mode": {"page_id": "", "page_type": "media_create", "operation": "click", "page_section": "", "target_type": "change_mode", "data": []}, "media_create_action_swipe": {"page_id": "", "page_type": "media_create", "operation": "action_swipe", "page_section": "", "target_type": "", "data": []}, "media_full_screen_view": {"page_id": "", "page_type": "media_full_screen", "operation": "view", "page_section": "", "target_type": "", "data": []}, "media_full_screen_click_back": {"page_id": "", "page_type": "media_full_screen", "operation": "click", "page_section": "", "target_type": "back", "data": []}, "media_full_screen_click_select_media": {"page_id": "", "page_type": "media_full_screen", "operation": "click", "page_section": "", "target_type": "select_media", "data": []}, "media_full_screen_click_next": {"page_id": "", "page_type": "media_full_screen", "operation": "click", "page_section": "", "target_type": "next", "data": []}, "media_edit_impression_edit_tool": {"page_id": "", "page_type": "media_edit", "operation": "impression", "page_section": "", "target_type": "edit_tool", "data": []}, "media_edit_click_upload": {"page_id": "", "page_type": "media_edit", "operation": "click", "page_section": "", "target_type": "upload", "data": []}, "media_edit_action_process_success": {"page_id": "", "page_type": "media_edit", "operation": "action_process_success", "page_section": "", "target_type": "", "data": []}, "media_edit_with_trim_view": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "view", "page_section": "", "target_type": "", "data": []}, "media_edit_with_trim_impression_save": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "impression", "page_section": "", "target_type": "save", "data": []}, "media_edit_with_trim_action_edit_media": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "action_edit_media", "page_section": "", "target_type": "", "data": []}, "media_edit_impression_auto_trim_popup": {"page_id": "", "page_type": "media_edit", "operation": "impression", "page_section": "auto_trim_popup", "target_type": "", "data": []}, "media_edit_click_auto_trim_popup_cancel": {"page_id": "", "page_type": "media_edit", "operation": "click", "page_section": "auto_trim_popup", "target_type": "cancel", "data": []}, "media_edit_click_auto_trim_popup_confirm": {"page_id": "", "page_type": "media_edit", "operation": "click", "page_section": "auto_trim_popup", "target_type": "confirm", "data": []}, "media_edit_with_trim_click_save": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "click", "page_section": "", "target_type": "save", "data": []}, "media_edit_with_trim_click_next": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "click", "page_section": "", "target_type": "next", "data": []}, "media_edit_with_trim_impression_next": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "impression", "page_section": "", "target_type": "next", "data": []}, "media_edit_with_trim_impression_back": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "impression", "page_section": "", "target_type": "back", "data": []}, "media_edit_with_trim_click_back": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "click", "page_section": "", "target_type": "back", "data": []}, "media_edit_with_trim_click_close": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "click", "page_section": "", "target_type": "close", "data": []}, "media_edit_with_trim_impression_close": {"page_id": "", "page_type": "media_edit_with_trim", "operation": "impression", "page_section": "", "target_type": "close", "data": []}, "media_edit_click_edit_tool": {"page_id": "", "page_type": "media_edit", "operation": "click", "page_section": "", "target_type": "edit_tool", "data": []}}