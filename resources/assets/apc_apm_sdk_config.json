{"anr_filter": "com.apm.application,com.apm", "net_url_filter": "", "clean_exp": 2, "clean_interval": 7200000, "cloud_interval": 3600000, "control_activity": 0, "debug": true, "file_data_dirs": "shared_prefs", "func_control": {"activity_first_min_time": 0, "activity_lifecycle_min_time": 0, "block_min_time": 4500, "file_dir_depth": 3, "io_min_time": 3000, "memory_delay_time": 10000, "min_file_size": 51200, "onreceive_min_time": 2000, "thread_min_time": 4000}, "g_core": {"activity": true, "anr": false, "appstart": true, "battery": true, "block": true, "cpu": true, "exp": 2158934400000, "fileinfo": true, "fps": true, "func": true, "io": true, "memory": true, "monitor": true, "net": true, "processinfo": true, "sfps": true, "webview": true, "watchdog": true, "threadcnt": true}, "once_max_count": 30, "pause_interval": 14400000, "random_control_time": 300000, "upload_interval": 14400000}