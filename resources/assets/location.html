<html>
<head>
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no"/>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <title>Location Detail</title>
    <script type="text/javascript"
            src="https://maps.googleapis.com/maps/api/js?sensor=false"></script>
    <script type="text/javascript">
  google.maps.visualRefresh = true;
  var map;
  var marker;
  function initialize() {
    var latitude = 0;
    var longitude = 0;
    if (window.android){
      latitude = window.android.getLatitude();
      longitude = window.android.getLongitude();
    }

    var myLatlng = new google.maps.LatLng(latitude,longitude);
    var myOptions = {
      zoom: 15,
      center: myLatlng,
      mapTypeId: google.maps.MapTypeId.ROADMAP
    }
    map = new google.maps.Map(document.getElementById("map_canvas"), myOptions);
  }

  function centerAt(latitude, longitude){
    myLatlng = new google.maps.LatLng(latitude,longitude);
    map.panTo(myLatlng);
    marker = new google.maps.Marker({
        map: map,
        position: myLatlng
    });
  }






    </script>
</head>
<body style="margin:0px; padding:0px;" onload="initialize()">
<div id="map_canvas" style="width:100%; height:100%"></div>
</body>
</html>