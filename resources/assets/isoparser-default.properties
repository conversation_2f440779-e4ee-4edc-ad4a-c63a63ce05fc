meta-ilst=com.coremedia.iso.boxes.apple.AppleItemListBox
rmra=com.coremedia.iso.boxes.apple.AppleReferenceMovieBox
rmda=com.coremedia.iso.boxes.apple.********************************
rmdr=com.coremedia.iso.boxes.apple.AppleDataRateBox
rdrf=com.coremedia.iso.boxes.apple.AppleDataReferenceBox

wave=com.coremedia.iso.boxes.apple.AppleWaveBox

udta-yrrc=com.coremedia.iso.boxes.RecordingYearBox
udta-titl=com.coremedia.iso.boxes.TitleBox
udta-dscp=com.coremedia.iso.boxes.DescriptionBox
udta-albm=com.coremedia.iso.boxes.AlbumBox
udta-cprt=com.coremedia.iso.boxes.CopyrightBox
udta-gnre=com.coremedia.iso.boxes.GenreBox
udta-perf=com.coremedia.iso.boxes.PerformerBox
udta-auth=com.coremedia.iso.boxes.AuthorBox
udta-kywd=com.coremedia.iso.boxes.KeywordsBox
udta-loci=com.coremedia.iso.boxes.threegpp26244.LocationInformationBox
udta-rtng=com.coremedia.iso.boxes.RatingBox
udta-clsf=com.coremedia.iso.boxes.ClassificationBox
udta-cdis=com.coremedia.iso.boxes.vodafone.ContentDistributorIdBox
udta-albr=com.coremedia.iso.boxes.vodafone.AlbumArtistBox



tx3g=com.coremedia.iso.boxes.sampleentry.TextSampleEntry
stsd-text=com.googlecode.mp4parser.boxes.apple.QuicktimeTextSampleEntry
enct=com.coremedia.iso.boxes.sampleentry.TextSampleEntry(type)
samr=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
sawb=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
mp4a=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
drms=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-alac=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
mp4s=com.coremedia.iso.boxes.sampleentry.MpegSampleEntry(type)
owma=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
ac-3=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
dac3=com.googlecode.mp4parser.boxes.AC3SpecificBox
ec-3=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
dec3=com.googlecode.mp4parser.boxes.EC3SpecificBox
stsd-lpcm=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsc=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsh=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsl=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
ddts=com.googlecode.mp4parser.boxes.DTSSpecificBox
stsd-dtse=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-mlpa=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
dmlp=com.googlecode.mp4parser.boxes.MLPSpecificBox
enca=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
sowt=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
encv=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
apcn=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
mp4v=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
s263=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
avc1=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
avc2=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
dvhe=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
dvav=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
avc3=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
avc4=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
hev1=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
hvc1=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
ovc1=com.coremedia.iso.boxes.sampleentry.Ovc1VisualSampleEntryImpl
avcC=com.coremedia.iso.boxes.h264.AvcConfigurationBox
alac=com.coremedia.iso.boxes.AppleLosslessSpecificBox
btrt=com.coremedia.iso.boxes.BitRateBox
ftyp=com.coremedia.iso.boxes.FileTypeBox
mdat=com.coremedia.iso.boxes.mdat.MediaDataBox
moov=com.coremedia.iso.boxes.MovieBox
mvhd=com.coremedia.iso.boxes.MovieHeaderBox
trak=com.coremedia.iso.boxes.TrackBox
tkhd=com.coremedia.iso.boxes.TrackHeaderBox
edts=com.coremedia.iso.boxes.EditBox
elst=com.coremedia.iso.boxes.EditListBox
mdia=com.coremedia.iso.boxes.MediaBox
mdhd=com.coremedia.iso.boxes.MediaHeaderBox
hdlr=com.coremedia.iso.boxes.HandlerBox
minf=com.coremedia.iso.boxes.MediaInformationBox
vmhd=com.coremedia.iso.boxes.VideoMediaHeaderBox
smhd=com.coremedia.iso.boxes.SoundMediaHeaderBox
sthd=com.coremedia.iso.boxes.SubtitleMediaHeaderBox
hmhd=com.coremedia.iso.boxes.HintMediaHeaderBox
dinf=com.coremedia.iso.boxes.DataInformationBox
dref=com.coremedia.iso.boxes.DataReferenceBox
url\ =com.coremedia.iso.boxes.DataEntryUrlBox
urn\ =com.coremedia.iso.boxes.DataEntryUrnBox
stbl=com.coremedia.iso.boxes.SampleTableBox
ctts=com.coremedia.iso.boxes.CompositionTimeToSample
stsd=com.coremedia.iso.boxes.SampleDescriptionBox
stts=com.coremedia.iso.boxes.TimeToSampleBox
stss=com.coremedia.iso.boxes.SyncSampleBox
stsc=com.coremedia.iso.boxes.SampleToChunkBox
stsz=com.coremedia.iso.boxes.SampleSizeBox
stco=com.coremedia.iso.boxes.StaticChunkOffsetBox
subs=com.coremedia.iso.boxes.SubSampleInformationBox
udta=com.coremedia.iso.boxes.UserDataBox
skip=com.coremedia.iso.boxes.FreeSpaceBox
tref=com.coremedia.iso.boxes.TrackReferenceBox
iloc=com.coremedia.iso.boxes.ItemLocationBox
idat=com.coremedia.iso.boxes.ItemDataBox

damr=com.coremedia.iso.boxes.sampleentry.AmrSpecificBox
meta=com.coremedia.iso.boxes.MetaBox
ipro=com.coremedia.iso.boxes.ItemProtectionBox
sinf=com.coremedia.iso.boxes.ProtectionSchemeInformationBox
frma=com.coremedia.iso.boxes.OriginalFormatBox
schi=com.coremedia.iso.boxes.SchemeInformationBox
odaf=com.coremedia.iso.boxes.OmaDrmAccessUnitFormatBox
schm=com.coremedia.iso.boxes.SchemeTypeBox
uuid=com.coremedia.iso.boxes.UserBox(userType)
free=com.coremedia.iso.boxes.FreeBox
styp=com.coremedia.iso.boxes.fragment.SegmentTypeBox
mvex=com.coremedia.iso.boxes.fragment.MovieExtendsBox
mehd=com.coremedia.iso.boxes.fragment.MovieExtendsHeaderBox
trex=com.coremedia.iso.boxes.fragment.TrackExtendsBox

moof=com.coremedia.iso.boxes.fragment.MovieFragmentBox
mfhd=com.coremedia.iso.boxes.fragment.MovieFragmentHeaderBox
traf=com.coremedia.iso.boxes.fragment.TrackFragmentBox
tfhd=com.coremedia.iso.boxes.fragment.TrackFragmentHeaderBox
trun=com.coremedia.iso.boxes.fragment.TrackRunBox
sdtp=com.coremedia.iso.boxes.SampleDependencyTypeBox
mfra=com.coremedia.iso.boxes.fragment.MovieFragmentRandomAccessBox
tfra=com.coremedia.iso.boxes.fragment.TrackFragmentRandomAccessBox
mfro=com.coremedia.iso.boxes.fragment.MovieFragmentRandomAccessOffsetBox
tfdt=com.coremedia.iso.boxes.fragment.TrackFragmentBaseMediaDecodeTimeBox
nmhd=com.coremedia.iso.boxes.NullMediaHeaderBox
gmhd=com.googlecode.mp4parser.boxes.apple.GenericMediaHeaderAtom
gmhd-text=com.googlecode.mp4parser.boxes.apple.GenericMediaHeaderTextAtom
gmin=com.googlecode.mp4parser.boxes.apple.BaseMediaInfoAtom
cslg=CompositionShiftLeastGreatestAtom
pdin=ProgressiveDownloadInformationBox
bloc=com.googlecode.mp4parser.boxes.dece.BaseLocationBox
ftab=com.googlecode.mp4parser.boxes.threegpp26245.FontTableBox
co64=com.coremedia.iso.boxes.ChunkOffset64BitBox
xml\ =com.coremedia.iso.boxes.XmlBox
avcn=com.googlecode.mp4parser.boxes.basemediaformat.AvcNalUnitStorageBox
ainf=com.googlecode.mp4parser.boxes.ultraviolet.AssetInformationBox
pssh=com.googlecode.mp4parser.boxes.cenc.ProtectionSystemSpecificHeaderBox
trik=TrickPlayBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.PiffSampleEncryptionBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.PiffTrackEncryptionBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.TfrfBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.TfxdBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.UuidBasedProtectionSystemSpecificHeaderBox
senc=com.googlecode.mp4parser.boxes.dece.SampleEncryptionBox
tenc=com.googlecode.mp4parser.boxes.basemediaformat.TrackEncryptionBox
amf0=com.googlecode.mp4parser.boxes.adobe.ActionMessageFormat0SampleEntryBox

#iods=com.googlecode.mp4parser.boxes.mp4.ObjectDescriptorBox
esds=com.googlecode.mp4parser.boxes.mp4.ESDescriptorBox

tmcd=com.googlecode.mp4parser.boxes.apple.TimeCodeBox
sidx=com.googlecode.mp4parser.boxes.threegpp26244.SegmentIndexBox

sbgp=com.googlecode.mp4parser.boxes.mp4.samplegrouping.SampleToGroupBox
sgpd=com.googlecode.mp4parser.boxes.mp4.samplegrouping.SampleGroupDescriptionBox



default=com.coremedia.iso.boxes.UnknownBox(type)



\u00A9nam=com.coremedia.iso.boxes.apple.AppleNameBox
\u00A9ART=com.coremedia.iso.boxes.apple.AppleArtistBox
\u00A9alb=com.coremedia.iso.boxes.apple.AppleAlbumBox
\u00A9gen=com.coremedia.iso.boxes.apple.AppleGenreBox
#\u00A9day=com.googlecode.mp4parser.boxes.apple.AppleRecordingYearBox
trkn=com.coremedia.iso.boxes.apple.AppleTrackNumberBox
cpil=com.coremedia.iso.boxes.apple.AppleCompilationBox
pgap=com.coremedia.iso.boxes.apple.AppleGaplessPlaybackBox
apID=com.coremedia.iso.boxes.apple.AppleAppleIdBox
cprt=com.coremedia.iso.boxes.apple.AppleCopyrightBox
desc=com.coremedia.iso.boxes.apple.AppleDescriptionBox
tven=com.coremedia.iso.boxes.apple.AppleTVEpisodeNumberBox
tvsn=com.coremedia.iso.boxes.apple.AppleTVSeasonBox
tves=com.coremedia.iso.boxes.apple.AppleTVEpisodeBox
soal=com.coremedia.iso.boxes.apple.AppleSortAlbumBox
purd=com.coremedia.iso.boxes.apple.ApplePurchaseDateBox
stik=com.coremedia.iso.boxes.apple.AppleMediaTypeBox


#added by Tobias Bley / UltraMixer (04/25/2014)
\u00A9cmt=com.coremedia.iso.boxes.apple.AppleCommentBox
\u00A9too=com.coremedia.iso.boxes.apple.AppleEncoderBox
\u00A9wrt=com.coremedia.iso.boxes.apple.AppleTrackAuthorBox
\u00A9grp=com.coremedia.iso.boxes.apple.AppleGroupingBox
covr=com.coremedia.iso.boxes.apple.AppleCoverBox
saio=com.coremedia.iso.boxes.apple.SampleAuxiliaryInformationOffsetsBox
saiz=com.coremedia.iso.boxes.apple.SampleAuxiliaryInformationSizesBox



hint=com.coremedia.iso.boxes.TrackReferenceTypeBox(type)
cdsc=com.coremedia.iso.boxes.TrackReferenceTypeBox(type)
hind=com.coremedia.iso.boxes.TrackReferenceTypeBox(type)
vdep=com.coremedia.iso.boxes.TrackReferenceTypeBox(type)
vplx=com.coremedia.iso.boxes.TrackReferenceTypeBox(type)
