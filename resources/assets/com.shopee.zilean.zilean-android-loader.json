{"protocol": "v1", "mode": "snap", "time": "2022-09-13 18:42:16.120", "version": "0.0.9-SNAPSHOT", "branch": "feature/firebase", "commitId": "94e1d1f2986b0e1541a36b04297b353517ccef0b", "moduleCommitId": "94e1d1f2986b0e1541a36b04297b353517ccef0b", "lastMessage": "[feature] wait onBaseContextAttached Finished then do other things", "extraInfo": "pods", "groupId": "com.shopee.zilean", "artifactId": "zilean-android-loader", "path": ":zilean-android:zilean-android-loader", "remoteUrl": "<EMAIL>:shopee/ssz-client/android/merchant-services/common/zilean.git"}