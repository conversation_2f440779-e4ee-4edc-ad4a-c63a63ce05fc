{"protocol": "v1", "mode": "release", "time": "2022-08-05 15:07:15.262", "version": "1.12.0", "branch": "master", "commitId": "0390ca07bdf305d3c269bbe00acf7c9efc75f9c3", "moduleCommitId": "0390ca07bdf305d3c269bbe00acf7c9efc75f9c3", "lastMessage": " #Update [SPV-14345] 可视化RecyclerView滑动与播放器交互的流程-更新判断方法", "extraInfo": "master", "groupId": "com.shopee.sz", "artifactId": "log", "path": ":log", "remoteUrl": "ssh://<EMAIL>:2222/shopee/ssz-client/android/toc/common.git"}