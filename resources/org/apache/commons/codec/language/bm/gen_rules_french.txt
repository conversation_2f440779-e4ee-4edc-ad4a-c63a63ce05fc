/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// GENERAL

// CONSONANTS
"lt" "u" "$" "(lt|)" // Renault
"c" "n" "$" "(k|)" // Tronc
//"f" "" "" "(f|)" // Clef
"d" "" "$" "(t|)" // Durand
"g" "n" "$" "(k|)" // Gang
"p" "" "$" "(p|)" // Trop, Champ
"r" "e" "$" "(r|)" // Barbier
"t" "" "$" "(t|)" // <PERSON>rat, Constant
"z" "" "$" "(s|)" 

"ds" "" "$" "(ds|)" 
"ps" "" "$" "(ps|)" // Champs
"rs" "e" "$" "(rs|)" 
"ts" "" "$" "(ts|)" 
"s" "" "$" "(s|)" // Denis

"x" "u" "$" "(ks|)" // Arnoux

"s" "[aeéèêiou]" "[^aeéèêiou]" "(s|)" // Deschamps, Malesherbes, Groslot
"t" "[aeéèêiou]" "[^aeéèêiou]" "(t|)" // Petitjean

"kh" "" "" "x" // foreign
"ph" "" "" "f"

"ç" "" "" "s"
"x" "" "" "ks"
"ch" "" "" "S"
"c" "" "[eiyéèê]" "s"

"gn" "" "" "(n|gn)"
"g" "" "[eiy]" "Z" 
"gue" "" "$" "k"     
"gu" "" "[eiy]" "g" 
"aill" "" "e" "aj" // non Jewish
"ll" "" "e" "(l|j)" // non Jewish
"que" "" "$" "k"
"qu" "" "" "k"
"s" "[aeiouyéèê]" "[aeiouyéèê]" "z"
"h" "[bdgt]" "" "" // translit from Arabic

"m" "[aeiouy]" "[aeiouy]" "m"  
"m" "[aeiouy]" "" "(m|n)"  // nasal

"ou" "" "[aeio]" "v" 
"u" "" "[aeio]" "v" 

// VOWELS
"aue" "" "" "aue" 
"eau" "" "" "o" 
"au" "" "" "(o|au)" // non Jewish
"ai" "" "" "(e|aj)" // [e] is non Jewish
"ay" "" "" "(e|aj)" // [e] is non Jewish
"é" "" "" "e"
"ê" "" "" "e"
"è" "" "" "e"
"à" "" "" "a"
"â" "" "" "a"
"où" "" "" "u"
"ou" "" "" "u"
"oi" "" "" "(oj|va)" // [va] (actually "ua") is non Jewish
"ei" "" "" "(aj|ej|e)" // [e] is non Jewish
"ey" "" "" "(aj|ej|e)" // [e] non Jewish
"eu" "" "" "(ej|Y)" // non Jewish
"y" "[ou]" "" "j"
"e" "" "$" "(e|)"
"i" "" "[aou]" "j"
"y" "" "[aoeu]" "j"

// LATIN ALPHABET      
"a" "" "" "a"
"b" "" "" "b"
"c" "" "" "k"
"d" "" "" "d"
"e" "" "" "e" 
"f" "" "" "f"
"g" "" "" "g"
"h" "" "" "h"
"i" "" "" "i" 
"j" "" "" "Z"
"k" "" "" "k"
"l" "" "" "l"
"m" "" "" "m"
"n" "" "" "n"
"o" "" "" "o"
"p" "" "" "p"
"q" "" "" "k"
"r" "" "" "r"
"s" "" "" "s"
"t" "" "" "t"
"u" "" "" "(u|Q)"
"v" "" "" "v"
"w" "" "" "v"
"y" "" "" "i"
"z" "" "" "z"
