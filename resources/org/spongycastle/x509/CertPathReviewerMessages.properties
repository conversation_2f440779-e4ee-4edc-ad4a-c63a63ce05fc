
## constructor exceptions 

# cert path is empty
CertPathReviewer.emptyCertPath.title = CertPath is empty
CertPathReviewer.emptyCertPath.text = PKIXCertPathReviewer: the CertPath is empty.
CertPathReviewer.emptyCertPath.summary = PKIXCertPathReviewer: the CertPath is empty.
CertPathReviewer.emptyCertPath.details = PKIXCertPathReviewer: the CertPath is empty.

## name constraints processing errors

# cert DN is not in the permitted tree
# {0} DN as String 
CertPathReviewer.notPermittedDN.title = Name constraint error: certificate DN is not permitted
CertPathReviewer.notPermittedDN.text = Name constraint error: the certificate DN {0} is not permitted.
CertPathReviewer.notPermittedDN.summary = Name constraint error: certificate DN is not permitted.
CertPathReviewer.notPermittedDN.details = Name constraint checking error. The certificate DN {0} is not in the permitted set of DNs.

# cert DN is in the excluded tree
# {0} DN as String
CertPathReviewer.excludedDN.title = Name constraint error: certificate DN is excluded
CertPathReviewer.excludedDN.text = Name constraint error: The certificate DN {0} is excluded.
CertPathReviewer.excludedDN.summary = Name constraint error: certificate DN is excluded.
CertPathReviewer.excludedDN.details = Name constraint checking error. The certificate DN {0} is inside of the excluded set of DNs.

# cert email is not in the permitted tree
# {0} email address as String
CertPathReviewer.notPermittedEmail.title = Name constraint error: not permitted email address
CertPathReviewer.notPermittedEmail.text = Name constraint error: certificate contains the not permitted email address {0}.
CertPathReviewer.notPermittedEmail.summary = Name constraint error: not permitted email address.
CertPathReviewer.notPermittedEmail.details = Name constraint checking error. The certificate contains the email address {0} which is not in the permitted set of email addresses.

# cert email is in the excluded tree
# {0} email as String
CertPathReviewer.excludedEmail.title = Name constraint error: excluded email address
CertPathReviewer.excludedEmail.text = Name constraint error: certificate contains the excluded email address {0}. 
CertPathReviewer.excludedEmail.summary = Name constraint error: excluded email address.
CertPathReviewer.excludedEmail.details = Name constraint checking error. The certificate contains the email address {0} which is in the excluded set of email addresses.

# cert IP is not in the permitted tree
# {0} ip address as String
CertPathReviewer.notPermittedIP.title = Name constraint error: not permitted IP address
CertPathReviewer.notPermittedIP.text = Name constraint error: certificate contains the not permitted IP address {0}.
CertPathReviewer.notPermittedIP.summary = Name constraint error: not permitted IP address.
CertPathReviewer.notPermittedIP.details = Name constraint checking error. The certificate contains the IP address {0} which is not in the permitted set of IP addresses.

# cert ip is in the excluded tree
# {0} ip address as String
CertPathReviewer.excludedIP.title = Name constraint error: excluded IP address
CertPathReviewer.excludedIP.text = Name constraint error: certificate contains the excluded IP address {0}.
CertPathReviewer.excludedIP.summary = Name constraint error: excluded IP address.
CertPathReviewer.excludedIP.details = Name constraint checking error. The certificate contains the IP address {0} which is in the excluded set of IP addresses.

# error processing the name constraints extension
CertPathReviewer.ncExtError.title = Name constraint checking failed
CertPathReviewer.ncExtError.text = Name constraint checking failed: there was an error processing the name constraints extension of the certificate.
CertPathReviewer.ncExtError.summary = Error processing the name constraints extension.
CertPathReviewer.ncExtError.details = Name constraint checking failed: there was an error processing the name constraints extension of the certificate.

# error processing the subject alternative name extension
CertPathReviewer.subjAltNameExtError.title = Name constraint checking failed
CertPathReviewer.subjAltNameExtError.text = Name constraint checking failed: there was an error processing the subject alternative name extension of the certificate.
CertPathReviewer.subjAltNameExtError.summary = Error processing the subject alternative name extension.
CertPathReviewer.subjAltNameExtError.details = Name constraint checking failed: there was an error processing the subject alternative name extension of the certificate.

# exception extracting subject name when checking subtrees
# {0} subject Principal
CertPathReviewer.ncSubjectNameError.title = Name constraint checking failed
CertPathReviewer.ncSubjectNameError.text = Name constraint checking failed: there was an exception extracting the DN from the certificate.
CertPathReviewer.ncSubjectNameError.summary = Name constraint checking failed: exception extracting the DN.
CertPathReviewer.ncSubjectNameError.details = Name constraint checking failed: there was an exception extracting the DN from the certificate.


## path length errors

# max path length extended
CertPathReviewer.pathLenghtExtended.title = Maximum path length extended 
CertPathReviewer.pathLenghtExtended.text = Certificate path invalid: Maximum path length extended.
CertPathReviewer.pathLenghtExtended.summary = Certificate path invalid: Maximum path length extended.
CertPathReviewer.pathLenghtExtended.details = Certificate path invalid: Maximum path length extended.

# error reading length constraint from basic constraint extension
CertPathReviewer.processLengthConstError.title = Path length checking failed
CertPathReviewer.processLengthConstError.text = Path length checking failed: there was an error processing the basic constraint extension of the certificate. 
CertPathReviewer.processLengthConstError.summary = Error processing the subject alternative name extension.
CertPathReviewer.processLengthConstError.details = Path length checking failed: there was an error processing the basic constraint extension of the certificate.


## path length notifications

# total path length as defined in rfc 3280
# {0} the path length as Integer
CertPathReviewer.totalPathLength.title = Total path length
CertPathReviewer.totalPathLength.text = The total path length without self-signed certificates is {0}.
CertPathReviewer.totalPathLength.summary = The total path length without self-signed certificates is {0}.
CertPathReviewer.totalPathLength.details = The total path length without self-signed certificates, as defined in RFC 3280, is {0}.


## critical extensions errors

# one unknown critical extension
# {0} extension as String
CertPathReviewer.unknownCriticalExt.title = Unknown critical extension
CertPathReviewer.unknownCriticalExt.text = The certificate contains the unknown critical extension {0}.
CertPathReviewer.unknownCriticalExt.summary = Unknown critical extension: {0}.
CertPathReviewer.unknownCriticalExt.details = The certificate contains the unknown critical extension with the OID {0}.

# more unknown critical extensions
# {0} extensions as Set of Strings
CertPathReviewer.unknownCriticalExts.title = Unknown critical extensions
CertPathReviewer.unknownCriticalExts.text = The certificate contains two or more unknown critical extensions: {0}.
CertPathReviewer.unknownCriticalExts.summary = Unknown critical extensions: {0}.
CertPathReviewer.unknownCriticalExts.details = The certificate contains two or more unknown critical extensions with the OIDs: {0}.

# error processing critical extension
# {0} the message of the underlying exception
# {1} the underlying exception
# {2} the name of the exception
CertPathReviewer.criticalExtensionError.title = Error processing a critical extension
CertPathReviewer.criticalExtensionError.text = Error processing a critical extension. A {0} occurred.
CertPathReviewer.criticalExtensionError.summary = Error processing a critical extension. A {0} occurred.
CertPathReviewer.criticalExtensionError.details = Error processing a critical extension. A {0} occurred. Cause: {0}.

# error initializing the certpath checkers
# {0} the message of the underlying exception
# {1} the underlying exception
# {2} the name of the exception
CertPathReviewer.certPathCheckerError.title = Checking critical extensions failed
CertPathReviewer.certPathCheckerError.text = Checking critical extensions failed: there was a {2} initializing a CertPathChecker.
CertPathReviewer.certPathCheckerError.summary = Checking critical extensions failed: {2} initializing a CertPathChecker
CertPathReviewer.certPathCheckerError.details = Checking critical extensions failed: there was an {2} initializing a CertPathChecker. Cause: {0}


## check signature errors

CertPathReviewer.rootKeyIsValidButNotATrustAnchor.title = Root key with valid signature but no trust anchor
CertPathReviewer.rootKeyIsValidButNotATrustAnchor.text = The certificate has a valid signature, but is no trust anchor
CertPathReviewer.rootKeyIsValidButNotATrustAnchor.summary = The certificate has a valid signature, but is no trust anchor
CertPathReviewer.rootKeyIsValidButNotATrustAnchor.details = The certificate has a valid signature, but is no trust anchor

# trustanchor found, but certificate validation failed
CertPathReviewer.trustButInvalidCert.title = Trust anchor found, but different public key
CertPathReviewer.trustButInvalidCert.text = A trust anchor was found. But it has a different public key, than was used to issue the first certificate of the cert path.
CertPathReviewer.trustButInvalidCert.summary = A trust anchor was found. But it has a different public key, than was used to issue the first certificate of the cert path.
CertPathReviewer.trustButInvalidCert.details = A trust anchor was found. But it has a different public key, than was used to issue the first certificate of the cert path.

# trustanchor - cannot extract issuer
CertPathReviewer.trustAnchorIssuerError.title = Finding trust anchor failed 
CertPathReviewer.trustAnchorIssuerError.text = Finding trust anchor failed: cannot extract issuer from certificate.
CertPathReviewer.trustAnchorIssuerError.summary = Finding trust anchor failed: cannot extract issuer from certificate.
CertPathReviewer.trustAnchorIssuerError.details = Finding trust anchor failed: cannot extract issuer from certificate.

# no trustanchor was found for the certificate path
# {0} issuer of the root certificate of the path
# {1} number of trusted root certificates (trustanchors) provided
CertPathReviewer.noTrustAnchorFound.title = No trusted root certificate found
CertPathReviewer.noTrustAnchorFound.text = The root certificate of the certificate path was issued by a CA that is not in the the trusted-root-certificate-store used for the path validation. The name of the CA is "{0}".
CertPathReviewer.noTrustAnchorFound.summary = The root certificate of the certificate path was issued by a CA that is not in the the trusted-root-certificate-store used for the path validation.
CertPathReviewer.noTrustAnchorFound.details = The root certificate of the certificate path was issued by a CA that is not in the the trusted-root-certificate-store used for the path validation. The name of the CA is "{0}". The trusted-root-certificate store contains {1} CA(s).

# conflicting trust anchors
# {0} number of trustanchors found (Integer)
# {1} the ca name
CertPathReviewer.conflictingTrustAnchors.title = Corrupt trust root store
CertPathReviewer.conflictingTrustAnchors.text = Warning: corrupt trust root store: There are {0} trusted public keys for the CA "{1}" - please ensure with CA which is the correct key.
CertPathReviewer.conflictingTrustAnchors.summary = Warning: corrupt trust root store: There are {0} trusted public keys for the CA "{1}" - please ensure with CA which is the correct key.
CertPathReviewer.conflictingTrustAnchors.details = Warning: corrupt trust root store: There are {0} trusted public keys for the CA "{1}" - please ensure with CA which is the correct key.

# trustanchor DN is invalid
# {0} DN of the Trustanchor
CertPathReviewer.trustDNInvalid.title = DN of TrustAnchor is improperly specified
CertPathReviewer.trustDNInvalid.text = The DN of the TrustAnchor is improperly specified: {0}.
CertPathReviewer.trustDNInvalid.summary = The DN of the TrustAnchor is improperly specified.
CertPathReviewer.trustDNInvalid.details = The DN of the TrustAnchor is improperly specified: {0}. It's not a valid X.500 name. See RFC 1779 or RFC 2253. 

# trustanchor public key algorithm error
CertPathReviewer.trustPubKeyError.title = Error processing public key of the trust anchor
CertPathReviewer.trustPubKeyError.text = Error processing public key of the trust anchor.
CertPathReviewer.trustPubKeyError.summary = Error processing public key of the trust anchor.
CertPathReviewer.trustPubKeyError.details = Error processing public key of the trust anchor. Could not extract the AlorithmIdentifier for the key.

# can not verifiy signature: issuer public key unknown
CertPathReviewer.NoIssuerPublicKey.title = Can not verify the certificate signature 
CertPathReviewer.NoIssuerPublicKey.text = Can not verify the certificate signature: Issuer public key is unknown.
CertPathReviewer.NoIssuerPublicKey.summary = Can not verify the certificate signature: Issuer public key is unknown.
CertPathReviewer.NoIssuerPublicKey.details = Can not verify the certificate signature: Issuer public key is unknown.

# signature can not be verified
# {0} message of the underlying exception (english)
# {1} the underlying exception
# {2} the name of the exception
CertPathReviewer.signatureNotVerified.title = Certificate signature invalid
CertPathReviewer.signatureNotVerified.text = The certificate signature is invalid. A {2} occurred.
CertPathReviewer.signatureNotVerified.summary = The certificate signature is invalid.
CertPathReviewer.signatureNotVerified.details = The certificate signature is invalid. A {2} occurred. Cause: {0}

# certificate expired
# {0} the date the certificate expired 
CertPathReviewer.certificateExpired.title = Certificate is expired
CertPathReviewer.certificateExpired.text = Could not validate the certificate. Certificate expired on {0,date} {0,time,full}.
CertPathReviewer.certificateExpired.summary = Certificate expired on {0,date} {0,time,full}.
CertPathReviewer.certificateExpired.details = Could not validate the certificate. Certificate expired on {0,date} {0,time,full}. 

# certificate not yet valid
# {0} the date from which on the certificate is valid
CertPathReviewer.certificateNotYetValid.title = Certificate is not yet valid
CertPathReviewer.certificateNotYetValid.text = Could not validate the certificate. Certificate is not valid until {0,date} {0,time,full}.
CertPathReviewer.certificateNotYetValid.summary = Certificate is not valid until {0,date} {0,time,full}.
CertPathReviewer.certificateNotYetValid.details = Could not validate the certificate. Certificate is not valid until {0,date} {0,time,full}. 

# certificate invalid issuer DN
# {0} expected issuer DN as String
# {1} found issuer DN as String
CertPathReviewer.certWrongIssuer.title = Issuer of certificate not valid
CertPathReviewer.certWrongIssuer.text = Issuer of certificate is not valid. Expected {0}, but found {1}. 
CertPathReviewer.certWrongIssuer.summary = Issuer of certificate is not valid. 
CertPathReviewer.certWrongIssuer.details = Issuer of certificate is not valid. Expected {0}, but found {1}.

# intermediate certificate is no ca cert
CertPathReviewer.noCACert.title = Certificate is no CA certificate
CertPathReviewer.noCACert.text = Intermediate certificate is no CA certificate.
CertPathReviewer.noCACert.summary = The certificate is no CA certificate.
CertPathReviewer.noCACert.details = The certificate is no CA certificate but used as one.

# cert laks basic constraints
CertPathReviewer.noBasicConstraints.title = Certificate has no basic constraints
CertPathReviewer.noBasicConstraints.text = Intermediate certificate has no basic constraints.
CertPathReviewer.noBasicConstraints.summary = Intermediate certificate has no basic constraints.
CertPathReviewer.noBasicConstraints.details = Intermediate certificate has no basic constraints.

# error processing basic constraints
CertPathReviewer.errorProcesingBC.title = Error processing the basic constraints extension
CertPathReviewer.errorProcesingBC.text = There was an error while processing the basic constraints extension of this certificate.
CertPathReviewer.errorProcesingBC.summary = Error processing the basic constraints extension. 
CertPathReviewer.errorProcesingBC.details = There was an error while processing the basic constraints extension of this certificate.

# certificate not usable for signing certs
CertPathReviewer.noCertSign.title = Key not usable for signing certificates
CertPathReviewer.noCertSign.text = The key usage constraint does not allow the use of this certificate key for signing certificates.
CertPathReviewer.noCertSign.summary = The certificate key can not be used for signing certificates.
CertPathReviewer.noCertSign.details = The key usage constraint does not allow the use of this certificate key for signing certificates.

# error processing public key
CertPathReviewer.pubKeyError.title = Error processing public key
CertPathReviewer.pubKeyError.text = Error processing public key of the certificate.
CertPathReviewer.pubKeyError.summary = Error processing public key of the certificate.
CertPathReviewer.pubKeyError.details = Error processing public key of the certificate. Could not extract the AlorithmIdentifier for the key.


## check signatures notifications

#
# trust anchor has no keyusage certSign
CertPathReviewer.trustKeyUsage.title = Trust anchor key usage
CertPathReviewer.trustKeyUsage.text = The trust anchor is not alloed to sign certificates. 
CertPathReviewer.trustKeyUsage.summary = The trust anchor is not alloed to sign certificates.
CertPathReviewer.trustKeyUsage.details = The trust anchor is not alloed to sign certificates.

# certificate path validation date
# {0} date for which the cert path is validated
# {1} current date
CertPathReviewer.certPathValidDate.title = Certificate path validation date
CertPathReviewer.certPathValidDate.text = The certificate path was applied on {0,date} {0,time,full}. It was checked at {1,date} {1,time,full}.
CertPathReviewer.certPathValidDate.summary = The certificate path was validated for {0,date} {0,time,full}. It was checked at {1,date} {1,time,full}.
CertPathReviewer.certPathValidDate.details = The certificate path was validated for {0,date} {0,time,full}. It was checked at {1,date} {1,time,full}.


## check policy errors

# error processing certificate policy extension
CertPathReviewer.policyExtError.title = Policy checking failed
CertPathReviewer.policyExtError.text = Policy checking failed: there was an error processing the certificate policy extension. 
CertPathReviewer.policyExtError.summary = Error processing the certificate policy extension.
CertPathReviewer.policyExtError.details = Policy checking failed: there was an error processing the certificate policy extension. 

# error processing policy constraints extension
CertPathReviewer.policyConstExtError.title = Policy checking failed
CertPathReviewer.policyConstExtError.text = Policy checking failed: there was an error processing the policy constraints extension.
CertPathReviewer.policyConstExtError.summary = Error processing the policy constraints extension.
CertPathReviewer.policyConstExtError.details = Policy checking failed: there was an error processing the policy constraints extension.

# error processing policy mapping extension
CertPathReviewer.policyMapExtError.title = Policy checking failed
CertPathReviewer.policyMapExtError.text = Policy checking failed: there was an error processing the policy mapping extension.
CertPathReviewer.policyMapExtError.summary = Error processing the policy mapping extension.
CertPathReviewer.policyMapExtError.details = Policy checking failed: there was an error processing the policy mapping extension.

# error processing inhibit any policy extension
CertPathReviewer.policyInhibitExtError.title = Policy checking failed
CertPathReviewer.policyInhibitExtError.text = Policy checking failed: there was an error processing the inhibit any policy extension.
CertPathReviewer.policyInhibitExtError.summary = Error processing the inhibit any policy extension.
CertPathReviewer.policyInhibitExtError.details = Policy checking failed: there was an error processing the inhibit any policy extension.

# error building qualifier set
CertPathReviewer.policyQualifierError.title = Policy checking failed
CertPathReviewer.policyQualifierError.text = Policy checking failed: error building the policy qualifier set.
CertPathReviewer.policyQualifierError.summary = Policy checking failed: error building the policy qualifier set.
CertPathReviewer.policyQualifierError.details = Policy checking failed: error building the policy qualifier set.

# no valid policy tree - explicit policy required
CertPathReviewer.noValidPolicyTree.title = Policy checking failed
CertPathReviewer.noValidPolicyTree.text = Policy checking failed: no valid policy tree found when one expected.
CertPathReviewer.noValidPolicyTree.summary = Policy checking failed: no valid policy tree found when one expected.
CertPathReviewer.noValidPolicyTree.details = Policy checking failed: no valid policy tree found when one expected.

# expicit policy requested, but no policy available
CertPathReviewer.explicitPolicy.title = Policy checking failed
CertPathReviewer.explicitPolicy.text = Policy checking failed: explicit policy requested but no policy available.
CertPathReviewer.explicitPolicy.summary = Policy checking failed: explicit policy requested but no policy available.
CertPathReviewer.explicitPolicy.details = Policy checking failed: explicit policy requested but no policy available.

# path processing failed on policy
CertPathReviewer.invalidPolicy.title = Path processing failed on policy
CertPathReviewer.invalidPolicy.text = Path processing failed on policy.
CertPathReviewer.invalidPolicy.summary = Path processing failed on policy.
CertPathReviewer.invalidPolicy.details = Path processing failed on policy.

# invalid policy mapping
CertPathReviewer.invalidPolicyMapping.title = Invalid policy mapping 
CertPathReviewer.invalidPolicyMapping.text = Certificate contains an invalid policy mapping.
CertPathReviewer.invalidPolicyMapping.summary = Certificate contains an invalid policy mapping. 
CertPathReviewer.invalidPolicyMapping.details = Certificate contains a policy mapping including the value any policy which is invalid.

## check CRL notifications

# found local valid CRL
# {0} thisUpdate of the CRL
# {1} nextUpdate of the CRL
CertPathReviewer.localValidCRL.title = Found valid local CRL
CertPathReviewer.localValidCRL.text = Found a valid CRL in local certstore. Issued on {0,date}, next update {1,date}.
CertPathReviewer.localValidCRL.summary = Found a valid CRL in local certstore. Issued on {0,date}, next update {1,date}.
CertPathReviewer.localValidCRL.details = Found a valid CRL in local certstore. Issued on {0,date}, next update {1,date}.


# found matching CRL, but not valid
# {0} thisUpdate of the CRL
# {1} nextUpdate of the CRL
CertPathReviewer.localInvalidCRL.title = Local CRL outdated
CertPathReviewer.localInvalidCRL.text = Did not use a matching CRL in a local certstore, because it is outdated. Issued on {0,date}, next update {1,date}.
CertPathReviewer.localInvalidCRL.summary = Did not use a matching CRL in a local certstore, because it is outdated. Issued on {0,date}, next update {1,date}.
CertPathReviewer.localInvalidCRL.details = Did not use a matching CRL in a local certstore, because it is outdated. Issued on {0,date}, next update {1,date}.

# found a valid crl at crl distribution point
# {0} thisUpdate of the CRL
# {1} nextUpdate of the CRL
# {2} the url of the distribution point
CertPathReviewer.onlineValidCRL.title = Found valid CRL at CRL distribution point
CertPathReviewer.onlineValidCRL.text = Found a valid CRL at: {2}. Issued on {0,date}, next update on {1,date}.
CertPathReviewer.onlineValidCRL.summary = Found a valid CRL at: {2}. Issued on {0,date}, next update on {1,date}.
CertPathReviewer.onlineValidCRL.details = Found a valid CRL at: {2}. Issued on {0,date}, next update on {1,date}.

# found an invalid CRL at crl distribution point
# {0} thisUpdate of the CRL
# {1} nextUpdate of the CRL
# {2} the url of the distribution point
CertPathReviewer.onlineInvalidCRL.title = Outdated CRL at CRL distribution point
CertPathReviewer.onlineInvalidCRL.text = The CRL loaded from {2} was outdated. Issued on {0,date}, next update on {1,date}.
CertPathReviewer.onlineInvalidCRL.summary = The CRL loaded from {2} was outdated. Issued on {0,date}, next update on {1,date}.
CertPathReviewer.onlineInvalidCRL.details = The CRL loaded from {2} was outdated. Issued on {0,date}, next update on {1,date}.

#found a CRL at a crl distribution point, but issued by another CA
# {0} issuer of the CRL
# {1} expected issuer
# {2} the url of the distribution point
CertPathReviewer.onlineCRLWrongCA.title = CRL from wrong issuer at CRL distribution point
CertPathReviewer.onlineCRLWrongCA.text = The CRL loaded from {2} has was issued by {0}, excpected {1}.
CertPathReviewer.onlineCRLWrongCA.summary = The CRL loaded from {2} has a wrong issuer.
CertPathReviewer.onlineCRLWrongCA.details = The CRL loaded from {2} has was issued by {0}, excpected {1}.

# Certificate not revoked
CertPathReviewer.notRevoked.title = Certificate not revoked
CertPathReviewer.notRevoked.text = The certificate was not revoked.
CertPathReviewer.notRevoked.summary = The certificate was not revoked.
CertPathReviewer.notRevoked.details = The certificate was not revoked.

# CRL found: certificate was revoked, but after the validationDate
# {0} the date the certificate was revoked
# {1} the reason for revoking the certificate
CertPathReviewer.revokedAfterValidation.title = Certificate was revoked after the validation date
CertPathReviewer.revokedAfterValidation.text = The certificate was revoked after the validation date at {0,date} {0,time,full}. Reason: {1}.
CertPathReviewer.revokedAfterValidation.summary = The certificate was revoked after the validation date at {0,date} {0,time,full}.
CertPathReviewer.revokedAfterValidation.details = The certificate was revoked after the validation date at {0,date} {0,time,full}. Reason: {1}.

# updated crl available
# {0} date since when the update is available
CertPathReviewer.crlUpdateAvailable.title = CRL update available
CertPathReviewer.crlUpdateAvailable.text = An update for the CRL of this certificate is available since {0,date} {0,time,full}.
CertPathReviewer.crlUpdateAvailable.summary = An update for the CRL of this certificate is available since {0,date} {0,time,full}.
CertPathReviewer.crlUpdateAvailable.details = An update for the CRL of this certificate is available since {0,date} {0,time,full}.

# crl distribution point url
# {0} the crl distribution point url as String
CertPathReviewer.crlDistPoint.title = CRL distribution point
CertPathReviewer.crlDistPoint.text = A CRL can be obtained from: {0}.
CertPathReviewer.crlDistPoint.summary = A CRL can be obtained from: {0}.
CertPathReviewer.crlDistPoint.details = A CRL can be obtained from: {0}.

# ocsp location
# {0} the url on which the ocsp service can be found
CertPathReviewer.ocspLocation.title = OCSP responder location
CertPathReviewer.ocspLocation.text = OCSP responder location: {0}.
CertPathReviewer.ocspLocation.summary = OCSP responder location: {0}.
CertPathReviewer.ocspLocation.details = OCSP responder location: {0}.

# unable to get crl from crl distribution point
# {0} the url of the distribution point
# {1} the message of the occurred exception
# {2} the occurred exception
# {3} the name of the exception
CertPathReviewer.loadCrlDistPointError.title = Cannot load CRL from CRL distribution point
CertPathReviewer.loadCrlDistPointError.text = Unable to load a CRL from: {0}. A {3} occurred.
CertPathReviewer.loadCrlDistPointError.summary = Unable to load a CRL from: {0}. A {3} occurred.
CertPathReviewer.loadCrlDistPointError.details = Unable to load a CRL from: {0}. A {3} occurred. Cause: {1}.

# no crl found in certstores
# {0} the issuers which we searched for
# {1} list of crl issuer names that are found in the certstores
# {2} number of crls in the certstores
CertPathReviewer.noCrlInCertstore.title = No matching CRL found in local CRL store
CertPathReviewer.noCrlInCertstore.text = No matching CRL was found in the provided local CRL store.
CertPathReviewer.noCrlInCertstore.summary = No matching CRL was found in the provided local CRL store.
CertPathReviewer.noCrlInCertstore.details = No matching CRL was found in the provided local CRL store. \
No CRL was found for the selector "{0}". The {2} CRL(s) in the certstores are from "{1}".


## check CRL exceptions

# cannot extract issuer from certificate
CertPathReviewer.crlIssuerException.title = CRL checking failed
CertPathReviewer.crlIssuerException.text = CRL checking failed: cannot extract issuer from certificate.
CertPathReviewer.crlIssuerException.summary = CRL checking failed: cannot extract issuer from certificate.
CertPathReviewer.crlIssuerException.details = CRL checking failed: cannot extract issuer from certificate.

# cannot extract crls
# {0} message from the underlying exception
# {1} the underlying exception
# {2} the name of the exception
CertPathReviewer.crlExtractionError.title = CRL checking failed
CertPathReviewer.crlExtractionError.text = CRL checking failed: Cannot extract CRL from CertStore. There was a {2}.
CertPathReviewer.crlExtractionError.summary = CRL checking failed: Cannot extract CRL from CertStore. There was a {2}.
CertPathReviewer.crlExtractionError.details = CRL checking failed: Cannot extract CRL from CertStore. There was a {2}. Cause: {0}.

# Issuer certificate key usage extension does not permit crl signing
CertPathReviewer.noCrlSigningPermited.title = CRL checking failed
CertPathReviewer.noCrlSigningPermited.text = CRL checking failed: issuer certificate does not permit CRL signing.
CertPathReviewer.noCrlSigningPermited.summary = CRL checking failed: issuer certificate does not permit CRL signing.
CertPathReviewer.noCrlSigningPermited.details = CRL checking failed: issuer certificate does not permit CRL signing.

# can not verify crl: issuer public key unknown
CertPathReviewer.crlNoIssuerPublicKey.title = CRL checking failed
CertPathReviewer.crlNoIssuerPublicKey.text = CRL checking failed: Can not verify the CRL: Issuer public key is unknown.
CertPathReviewer.crlNoIssuerPublicKey.summary = CRL checking failed: Can not verify the CRL: Issuer public key is unknown.
CertPathReviewer.crlNoIssuerPublicKey.details = CRL checking failed: Can not verify the CRL: Issuer public key is unknown.

# crl verification failed
CertPathReviewer.crlVerifyFailed.title = CRL checking failed
CertPathReviewer.crlVerifyFailed.text = CRL checking failed: CRL signature is invalid.
CertPathReviewer.crlVerifyFailed.summary = CRL checking failed: CRL signature is invalid.
CertPathReviewer.crlVerifyFailed.details = CRL checking failed: CRL signature is invalid.

# no valid CRL found
CertPathReviewer.noValidCrlFound.title = CRL checking failed
CertPathReviewer.noValidCrlFound.text = CRL checking failed: no valid CRL found.
CertPathReviewer.noValidCrlFound.summary = CRL checking failed: no valid CRL found.
CertPathReviewer.noValidCrlFound.details = CRL checking failed: no valid CRL found.

# No base CRL for delta CRL
CertPathReviewer.noBaseCRL.title = CRL checking failed
CertPathReviewer.noBaseCRL.text = CRL checking failed: no base CRL found for delta CRL.
CertPathReviewer.noBaseCRL.summary = CRL checking failed: no base CRL found for delta CRL.
CertPathReviewer.noBaseCRL.details = CRL checking failed: no base CRL found for delta CRL.

# certificate revoked
# {0} the date the certificate was revoked
# {1} the reason for revoking the certificate
CertPathReviewer.certRevoked.title = Certificate was revoked
CertPathReviewer.certRevoked.text = The certificate was revoked at {0,date} {0,time,full}. Reason: {1}.
CertPathReviewer.certRevoked.summary = The certificate was revoked at {0,date} {0,time,full}.
CertPathReviewer.certRevoked.details = The certificate was revoked at {0,date} {0,time,full}. Reason: {1}.

# error processing issuing distribution point extension
CertPathReviewer.distrPtExtError.title = CRL checking failed
CertPathReviewer.distrPtExtError.text = CRL checking failed: there was an error processing the issuing distribution point extension. 
CertPathReviewer.distrPtExtError.summary = Error processing the issuing distribution point extension.
CertPathReviewer.distrPtExtError.details = CRL checking failed: there was an error processing the issuing distribution point extension.

# error processing crl distribution points extension
CertPathReviewer.crlDistPtExtError.title = CRL checking failed
CertPathReviewer.crlDistPtExtError.text = CRL checking failed: there was an error processing the crl distribution points extension.
CertPathReviewer.crlDistPtExtError.summary = Error processing the crl distribution points extension.
CertPathReviewer.crlDistPtExtError.details = CRL checking failed: there was an error processing the crl distribution points extension.

# error processing the authority info access extension
CertPathReviewer.crlAuthInfoAccError.title = CRL checking failed
CertPathReviewer.crlAuthInfoAccError.text = CRL checking failed: there was an error processing the authority info access extension.
CertPathReviewer.crlAuthInfoAccError.summary = Error processing the authority info access extension.
CertPathReviewer.crlAuthInfoAccError.details = CRL checking failed: there was an error processing the authority info access extension.

# error processing delta crl indicator extension
CertPathReviewer.deltaCrlExtError.title = CRL checking failed
CertPathReviewer.deltaCrlExtError.text = CRL checking failed: there was an error processing the delta CRL indicator extension. 
CertPathReviewer.deltaCrlExtError.summary = Error processing the delta CRL indicator extension.
CertPathReviewer.deltaCrlExtError.details = CRL checking failed: there was an error processing the delta CRL indicator extension.

# error porcessing crl number extension
CertPathReviewer.crlNbrExtError.title = CRL checking failed
CertPathReviewer.crlNbrExtError.text = CRL checking failed: there was an error processing the CRL number extension.
CertPathReviewer.crlNbrExtError.summary = Error processing the CRL number extension.
CertPathReviewer.crlNbrExtError.details = CRL checking failed: there was an error processing the CRL number extension.

# error processing crl reason code extension
CertPathReviewer.crlReasonExtError.title = CRL checking failed
CertPathReviewer.crlReasonExtError.text = CRL checking failed: there was an error processing the CRL reason code extension.
CertPathReviewer.crlReasonExtError.summary = Error processing the CRL reason code extension.
CertPathReviewer.crlReasonExtError.details = CRL checking failed: there was an error processing the CRL reason code extension.

# error processing basic constraints extension
CertPathReviewer.crlBCExtError.title = CRL checking failed
CertPathReviewer.crlBCExtError.text = CRL checking failed: there was an error processing the basic constraints extension.
CertPathReviewer.crlBCExtError.summary = Error processing the basic constraints extension.
CertPathReviewer.crlBCExtError.details = CRL checking failed: there was an error processing the basic constraints extension.

# CA Cert CRL only contains user certificates
CertPathReviewer.crlOnlyUserCert.title = CRL checking failed
CertPathReviewer.crlOnlyUserCert.text = CRL checking failed: CRL only contains user certificates.
CertPathReviewer.crlOnlyUserCert.summary = CRL checking failed: CRL only contains user certificates.
CertPathReviewer.crlOnlyUserCert.details = CRL checking failed: CRL for CA certificate only contains user certificates.

# End CRL only contains CA certificates
CertPathReviewer.crlOnlyCaCert.title = CRL checking failed
CertPathReviewer.crlOnlyCaCert.text = CRL checking failed: CRL only contains CA certificates.
CertPathReviewer.crlOnlyCaCert.summary = CRL checking failed: CRL only contains CA certificates.
CertPathReviewer.crlOnlyCaCert.details = CRL checking failed: CRL for end certificate only contains CA certificates.

# onlyContainsAttributeCerts boolean is asserted
CertPathReviewer.crlOnlyAttrCert.title = CRL checking failed
CertPathReviewer.crlOnlyAttrCert.text = CRL checking failed: CRL only contains attribute certificates.
CertPathReviewer.crlOnlyAttrCert.summary = CRL checking failed: CRL only contains attribute certificates.
CertPathReviewer.crlOnlyAttrCert.details = CRL checking failed: CRL only contains attribute certificates.


## QcStatement notifications

# unkown statement
# {0} statement OID
# {1} statement as ANS1Sequence
CertPathReviewer.QcUnknownStatement.title = Unknown statement in QcStatement extension 
CertPathReviewer.QcUnknownStatement.text = Unknown statement in QcStatement extension: OID = {0}
CertPathReviewer.QcUnknownStatement.summary = Unknown statement in QcStatement extension: OID = {0}
CertPathReviewer.QcUnknownStatement.details = Unknown statement in QcStatement extension: OID = {0}, statement = {1}

# QcLimitValue Alpha currency code
# {0} currency code
# {1} limit value
# {2} monetary value as MonetaryValue
CertPathReviewer.QcLimitValueAlpha.title = Transaction Value Limit
CertPathReviewer.QcLimitValueAlpha.text = This certificate has a limit for the transaction value: {1,number, ###,###,###,##0.00#} {0}.
CertPathReviewer.QcLimitValueAlpha.summary = Transaction value limit: {1,number, ###,###,###,##0.00#} {0}.
CertPathReviewer.QcLimitValueAlpha.details = This certificate has a limitation on the value of transaction for which this certificate can be used to the specified amount, according to the Directive 1999/93/EC of the European Parliament and of the Council of 13 December 1999 on a Community framework for electronic signatures, as implemented in the law of the country specified in the issuer field of this certificate. The limit for this certificate is {1,number, ###,###,###,##0.00#} {0}.

# QcLimitValue Numeric currency code
# {0} currency code
# {1} limit value
# {2} monetary value as MonetaryValue
CertPathReviewer.QcLimitValueNum.title = Transaction Value Limit
CertPathReviewer.QcLimitValueNum.text = This certificate has a limit for the transaction value: {1,number, ###,###,###,##0.00#} of currency {0} (See RFC 4217 for currency codes).
CertPathReviewer.QcLimitValueNum.summary = Transaction value limit: {1,number, ###,###,###,##0.00#} of currency {0} (See RFC 4217 for currency codes).
CertPathReviewer.QcLimitValueNum.details = This certificate has a limitation on the value of transaction for which this certificate can be used to the specified amount, according to the Directive 1999/93/EC of the European Parliament and of the Council of 13 December 1999 on a Community framework for electronic signatures, as implemented in the law of the country specified in the issuer field of this certificate. The limit for this certificate is {1,number, ###,###,###,##0.00#} of currency {0} (See RFC 4217 for currency codes).

# QcSSCD
CertPathReviewer.QcSSCD.title = QcSSCD Statement
CertPathReviewer.QcSSCD.text = (SSCD) The issuer claims that for the certificate where this statement appears that the private key associated with the public key in the certificate is protected according to Annex III of the Directive 1999/93/EC of the European Parliament and of the Council of 13 December 1999 on a Community framework for electronic signatures.
CertPathReviewer.QcSSCD.summary = (SSCD) The issuer claims that for the certificate where this statement appears that the private key associated with the public key in the certificate is protected according to Annex III of the Directive 1999/93/EC of the European Parliament and of the Council of 13 December 1999 on a Community framework for electronic signatures.
CertPathReviewer.QcSSCD.details = (SSCD) The issuer claims that for the certificate where this statement appears that the private key associated with the public key in the certificate is protected according to Annex III of the Directive 1999/93/EC of the European Parliament and of the Council of 13 December 1999 on a Community framework for electronic signatures.

# QcEuCompliance
CertPathReviewer.QcEuCompliance.title = Qualified Certificate
CertPathReviewer.QcEuCompliance.text = This certificate is issued as a Qualified Certificate according Annex I and II of the Directive 1999/93/EC of the European Parliament and of the Council of 13 December 1999 on a Community framework for electronic signatures, as implemented in the law of the country specified in the issuer field of this certificate.
CertPathReviewer.QcEuCompliance.summary = This certificate is issued as a Qualified Certificate according Annex I and II of the Directive 1999/93/EC of the European Parliament and of the Council of 13 December 1999 on a Community framework for electronic signatures, as implemented in the law of the country specified in the issuer field of this certificate.
CertPathReviewer.QcEuCompliance.details = This certificate is issued as a Qualified Certificate according Annex I and II of the Directive 1999/93/EC of the European Parliament and of the Council of 13 December 1999 on a Community framework for electronic signatures, as implemented in the law of the country specified in the issuer field of this certificate. 

## QcStatement errors

# error processing the QcStatement extension
CertPathReviewer.QcStatementExtError.title = Error processing the qc statements extension
CertPathReviewer.QcStatementExtError.text = Error processing the qc statements extension.
CertPathReviewer.QcStatementExtError.summary = Error processing the qc statements extension.
CertPathReviewer.QcStatementExtError.details = Error processing the qc statements extension.

## unknown/generic errors
CertPathReviewer.unknown.title = Unexpected Error 
CertPathReviewer.unknown.text = Unexpected Error {0}
CertPathReviewer.unknown.summary = Unexpected Error 
CertPathReviewer.unknown.details = Unexpected Error {0}

#
# crl reasons
#
unspecified = Unspecified
keyCompromise = Key Compromise
cACompromise = CA Compromise
affiliationChanged = Affiliation Changed
superseded = Superseded
cessationOfOperation = Cessation of Operation
certificateHold = Certificate Hold
unknown = Unknown
removeFromCRL = Remove from CRL
privilegeWithdrawn = Privilege Withdrawn
aACompromise = AA Compromise

#
#
#
missingIssuer = The missing certificate was issued by
missingSerial = with the serial number
 