// Helper script to update shopee_fcm_sender.js with <PERSON>ida captured data
// Usage: node update_config.js <frida_data.json>

const fs = require('fs');
const path = require('path');

function updateConfigFromFridaData(fridaDataFile) {
    try {
        // Read Frida captured data
        const fridaData = JSON.parse(fs.readFileSync(fridaDataFile, 'utf8'));
        console.log('Loaded Frida data from:', fridaDataFile);
        
        // Read current Node.js sender file
        const senderFile = 'shopee_fcm_sender.js';
        let senderContent = fs.readFileSync(senderFile, 'utf8');
        
        // Update server configuration
        if (fridaData.serverHost) {
            senderContent = senderContent.replace(
                /this\.serverHost = '[^']*'/,
                `this.serverHost = '${fridaData.serverHost}'`
            );
            console.log('✓ Updated server host:', fridaData.serverHost);
        }
        
        if (fridaData.serverPort) {
            senderContent = senderContent.replace(
                /this\.serverPort = \d+/,
                `this.serverPort = ${fridaData.serverPort}`
            );
            console.log('✓ Updated server port:', fridaData.serverPort);
        }
        
        // Update user info
        if (fridaData.userInfo && Object.keys(fridaData.userInfo).length > 0) {
            const userInfoStr = JSON.stringify(fridaData.userInfo, null, 12)
                .replace(/^/gm, '            '); // Indent for proper formatting
            
            senderContent = senderContent.replace(
                /this\.userInfo = \{[^}]*\};/s,
                `this.userInfo = ${userInfoStr.trim()};`
            );
            console.log('✓ Updated user info');
        }
        
        // Update device info
        if (fridaData.deviceInfo && Object.keys(fridaData.deviceInfo).length > 0) {
            // Convert byte array to base64 string if needed
            if (fridaData.deviceInfo.deviceFingerprint && Array.isArray(fridaData.deviceInfo.deviceFingerprint)) {
                fridaData.deviceInfo.deviceFingerprint = Buffer.from(fridaData.deviceInfo.deviceFingerprint).toString('base64');
            }
            
            const deviceInfoStr = JSON.stringify(fridaData.deviceInfo, null, 12)
                .replace(/^/gm, '            '); // Indent for proper formatting
            
            senderContent = senderContent.replace(
                /this\.deviceInfo = \{[^}]*\};/s,
                `this.deviceInfo = ${deviceInfoStr.trim()};`
            );
            console.log('✓ Updated device info');
        }
        
        // Update FCM token
        if (fridaData.fcmToken) {
            senderContent = senderContent.replace(
                /this\.fcmToken = '[^']*'/,
                `this.fcmToken = '${fridaData.fcmToken}'`
            );
            console.log('✓ Updated FCM token');
        }
        
        // Write updated file
        fs.writeFileSync(senderFile, senderContent);
        console.log('✓ Updated', senderFile);
        
        // Create backup of original data
        const backupFile = `frida_data_backup_${Date.now()}.json`;
        fs.writeFileSync(backupFile, JSON.stringify(fridaData, null, 2));
        console.log('✓ Created backup:', backupFile);
        
        console.log('\n✅ Configuration update completed!');
        console.log('You can now run: node shopee_fcm_sender.js');
        
    } catch (error) {
        console.error('❌ Error updating configuration:', error.message);
        process.exit(1);
    }
}

// Command line usage
if (require.main === module) {
    const fridaDataFile = process.argv[2];
    
    if (!fridaDataFile) {
        console.log('Usage: node update_config.js <frida_data.json>');
        console.log('');
        console.log('Steps:');
        console.log('1. Run Frida script: frida -U -f com.shopee.vn -l frida_capture_shopee_data.js --no-pause');
        console.log('2. In Frida console, call: exportShopeeData()');
        console.log('3. Save the JSON output to a file (e.g., frida_data.json)');
        console.log('4. Run this script: node update_config.js frida_data.json');
        process.exit(1);
    }
    
    if (!fs.existsSync(fridaDataFile)) {
        console.error('❌ Frida data file not found:', fridaDataFile);
        process.exit(1);
    }
    
    updateConfigFromFridaData(fridaDataFile);
}

module.exports = { updateConfigFromFridaData };
