const net = require('net');
const crypto = require('crypto');

class ShopeeFCMSender {
    constructor() {
        // Server configuration - UPDATE THESE FROM FRIDA SCRIPT
        this.serverHost = 'partner.api.shopee.vn'; // Get from Frida
        this.serverPort = 20443; // SSL port or 20080 for non-SSL
        this.useSSL = true;

        // User information - UPDATE FROM FRIDA SCRIPT
        this.userInfo = {
            userId: 1251243212, // Get from loggedInUser.getUserId()
            tocUserId: 0, // Get from loggedInUser.getTocUserId()
            token: 'B:EDgeLLCKk5DA+1aRWmyMaoMSPTtTDrdGQOLwyl4MQmqbf+tEc7ni0h3Y3ISuGZWS9e1rXyXWe+rP/SSnXE8AxuTyEOIvdwLrXRFUjQj8/7//390icAJutXcQKypzM4oSToA2gwJGyA==', // Get from loggedInUser.getToken()
            username: '', // Get from loggedInUser.getUsername()
            country: 'VN', // Usually VN for Vietnam
            language: 'vi' // Get from device settings
        };

        // Device information - UPDATE FROM FRIDA SCRIPT
        this.deviceInfo = {
            deviceId: 'VGVzdERldmljZUlE', // Get from deviceStore.k() - Base64 encoded (placeholder)
            deviceFingerprint: '', // Get from deviceStore.m()
            clientId: '', // Get from deviceStore.g()
            userAgent: 'ShopeeApp/3.23.0 (Android)', // Get from k.m().c()
            machineCode: 'android_gcm', // or 'android_gpns'
            appVersion: 32300,
            isRooted: false, // Get from q1.d()
            fingerprintBeforeTemper: null,
            isFingerprintTempered: false
        };

        // FCM Token - UPDATE FROM FRIDA SCRIPT
        this.fcmToken = 'eVhagjuZSouEABSoqdn_KA:APA91bHPlEeDno5_d_Y7pnOLi8WDyyugWRh_uCSUj9zj1CXUm42kJYMxduBnOpSFjiwlKVpM8QTzix9BK4eXmY7dyOQZFrYVvsYTChqz_bCK0TLhiCG3QqE';
    }

    // Generate request ID similar to Shopee's format
    generateRequestId() {
        return crypto.randomUUID().replace(/-/g, '').substring(0, 16);
    }

    // Create SetUserInfo protobuf-like payload
    createSetUserInfoPayload() {
        const requestId = this.generateRequestId();

        // Helper function to safely create buffer
        const safeBuffer = (data, encoding = 'utf8') => {
            if (!data) return Buffer.alloc(0);
            if (encoding === 'base64') {
                try {
                    return Buffer.from(data, 'base64');
                } catch (e) {
                    console.warn('Invalid base64 data, using empty buffer');
                    return Buffer.alloc(0);
                }
            }
            return Buffer.from(String(data), encoding);
        };

        // This is a simplified representation - actual protobuf encoding needed
        const payload = {
            requestid: requestId,
            token: this.userInfo.token || '',
            country: this.userInfo.country || 'VN',
            portrait: '',
            machine_code: this.deviceInfo.machineCode || 'android_gcm',
            deviceid: safeBuffer(this.deviceInfo.deviceId, 'base64'),
            pn_option: 0,
            language: this.userInfo.language || 'vi',
            phone_public: false,
            pn_token: safeBuffer(this.fcmToken),
            extinfo: Buffer.alloc(0),
            status: 0,
            bankacc_verified: 0,
            appversion: this.deviceInfo.appVersion || 32300,
            not_merge_extinfo: false,
            user_name: this.userInfo.username || '',
            cb_option: 0,
            ext: {
                deviceid: safeBuffer(this.deviceInfo.deviceId, 'base64'),
                device_fingerprint: safeBuffer(this.deviceInfo.deviceFingerprint),
                user_agent: this.deviceInfo.userAgent || '',
                is_rooted: this.deviceInfo.isRooted || false,
                fingerprint_before_temper: this.deviceInfo.fingerprintBeforeTemper,
                is_fingerprint_tempered: this.deviceInfo.isFingerprintTempered || false
            }
        };

        return { payload, requestId };
    }

    // Create TCP packet (simplified - needs proper protobuf encoding)
    createTCPPacket(payload) {
        // Command 67 for SetUserInfo
        const command = 67;

        // This is simplified - actual implementation needs protobuf encoding
        const payloadStr = JSON.stringify(payload);
        const payloadBuffer = Buffer.from(payloadStr, 'utf8');

        // Create packet header (simplified)
        const header = Buffer.alloc(8);
        header.writeUInt32LE(payloadBuffer.length + 4, 0); // Packet length
        header.writeUInt32LE(command, 4); // Command

        return Buffer.concat([header, payloadBuffer]);
    }

    // Send FCM token registration to server
    async sendFCMRegistration() {
        return new Promise((resolve, reject) => {
            // Validate configuration first
            const errors = this.validateConfig();
            if (errors.length > 0) {
                reject(new Error('Configuration errors: ' + errors.join(', ')));
                return;
            }

            console.log('Creating SetUserInfo payload...');
            const { payload, requestId } = this.createSetUserInfoPayload();

            console.log('Payload created:', {
                requestId,
                fcmToken: this.fcmToken ? this.fcmToken.substring(0, 20) + '...' : 'MISSING',
                userId: this.userInfo.userId || 'MISSING',
                deviceId: this.deviceInfo.deviceId ? this.deviceInfo.deviceId.substring(0, 10) + '...' : 'MISSING',
                serverHost: this.serverHost,
                serverPort: this.serverPort
            });

            // Create TCP packet
            const packet = this.createTCPPacket(payload);

            console.log('Connecting to server:', this.serverHost + ':' + this.serverPort);

            // Connect to server
            const client = net.createConnection({
                host: this.serverHost,
                port: this.serverPort
            });

            client.on('connect', () => {
                console.log('✓ Connected to Shopee server');
                console.log('Sending FCM registration packet...');
                client.write(packet);
            });

            client.on('data', (data) => {
                console.log('✓ Received response from server:', data.length, 'bytes');
                console.log('Response hex:', data.toString('hex'));
                client.end();
                resolve(data);
            });

            client.on('error', (err) => {
                console.error('✗ Connection error:', err.message);
                reject(err);
            });

            client.on('close', () => {
                console.log('Connection closed');
            });

            // Timeout after 30 seconds
            setTimeout(() => {
                if (!client.destroyed) {
                    client.destroy();
                    reject(new Error('Connection timeout after 30 seconds'));
                }
            }, 30000);
        });
    }

    // Update configuration from Frida script output
    updateFromFridaData(fridaData) {
        if (fridaData.serverHost) this.serverHost = fridaData.serverHost;
        if (fridaData.serverPort) this.serverPort = fridaData.serverPort;

        if (fridaData.userInfo) {
            Object.assign(this.userInfo, fridaData.userInfo);
        }

        if (fridaData.deviceInfo) {
            Object.assign(this.deviceInfo, fridaData.deviceInfo);
        }

        if (fridaData.fcmToken) {
            this.fcmToken = fridaData.fcmToken;
        }

        console.log('Configuration updated from Frida data');
    }

    // Validate configuration
    validateConfig() {
        const errors = [];

        if (!this.serverHost || this.serverHost === 'YOUR_SERVER_HOST') {
            errors.push('Server host not configured');
        }

        if (!this.fcmToken || this.fcmToken === 'YOUR_FCM_TOKEN_HERE') {
            errors.push('FCM token not configured');
        }

        if (!this.userInfo.userId || !this.userInfo.token) {
            errors.push('User info not configured');
        }

        // if (!this.deviceInfo.deviceId || !this.deviceInfo.deviceFingerprint) {
        //     errors.push('Device info not configured');
        // }

        return errors;
    }
}

// Example usage
async function main() {
    const sender = new ShopeeFCMSender();

    // Check if we have command line arguments for Frida data
    if (process.argv[2]) {
        try {
            const fridaData = JSON.parse(process.argv[2]);
            sender.updateFromFridaData(fridaData);
            console.log('✓ Updated configuration from command line data');
        } catch (e) {
            console.error('✗ Error parsing Frida data from command line:', e.message);
        }
    }

    // Validate configuration
    const errors = sender.validateConfig();
    if (errors.length > 0) {
        console.error('❌ Configuration errors:');
        errors.forEach(error => console.error('  - ' + error));
        console.log('\n📋 Steps to fix:');
        console.log('1. Run: frida -U -f com.shopee.vn -l frida_simple_capture.js --no-pause');
        console.log('2. Use the Shopee app (login, navigate)');
        console.log('3. In Frida console: exportData()');
        console.log('4. Save JSON output to file: captured_data.json');
        console.log('5. Run: node update_config.js captured_data.json');
        console.log('6. Then run: node shopee_fcm_sender.js');
        return;
    }

    // Show current configuration
    console.log('📊 Current Configuration:');
    console.log('  Server:', sender.serverHost + ':' + sender.serverPort);
    console.log('  User ID:', sender.userInfo.userId || 'Not set');
    console.log('  FCM Token:', sender.fcmToken ? 'Set (' + sender.fcmToken.length + ' chars)' : 'Not set');
    console.log('  Device ID:', sender.deviceInfo.deviceId ? 'Set' : 'Not set');
    console.log('');

    try {
        console.log('🚀 Starting FCM registration...');
        const response = await sender.sendFCMRegistration();
        console.log('✅ FCM registration completed successfully');
        console.log('📦 Response size:', response.length, 'bytes');
    } catch (error) {
        console.error('❌ FCM registration failed:', error.message);

        // Provide helpful error messages
        if (error.message.includes('ENOTFOUND')) {
            console.log('💡 Tip: Server host might be incorrect. Check network capture.');
        } else if (error.message.includes('ECONNREFUSED')) {
            console.log('💡 Tip: Server might be blocking connections or port is wrong.');
        } else if (error.message.includes('timeout')) {
            console.log('💡 Tip: Server might be slow or not responding.');
        }
    }
}

// Export for use as module
module.exports = ShopeeFCMSender;

// Run if called directly
if (require.main === module) {
    main();
}
