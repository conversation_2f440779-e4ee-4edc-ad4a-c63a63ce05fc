const net = require('net');
const crypto = require('crypto');

class ShopeeFCMSender {
    constructor() {
        // Server configuration - UPDATE THESE FROM FRIDA SCRIPT
        this.serverHost = 'partner.api.shopee.vn'; // Get from Frida
        this.serverPort = 20443; // SSL port or 20080 for non-SSL
        this.useSSL = true;
        
        // User information - UPDATE FROM FRIDA SCRIPT
        this.userInfo = {
            userId: 1218807615, // Get from loggedInUser.getUserId()
            tocUserId: 0, // Get from loggedInUser.getTocUserId()
            token: 'B:EMiPQ9VoxY21WxyKIcoXnQTNzUIiCYmJRIjsc/WlxTs91GssKKSUBo7F7n5F1+/KoyklwS24EHe4a3VzAAEI+ZPl+Zqrl0FRvC9kQU9DvYVwrc4jZb5JDlG4J5V3C5LpaHvYlSpWXg==', // Get from loggedInUser.getToken()
            username: '', // Get from loggedInUser.getUsername()
            country: 'VN', // Usually VN for Vietnam
            language: 'vi' // Get from device settings
        };
        
        // Device information - UPDATE FROM FRIDA SCRIPT
        this.deviceInfo = {
            deviceId: '123456', // Get from deviceStore.k() - Base64 encoded
            deviceFingerprint: '', // Get from deviceStore.m()
            clientId: '', // Get from deviceStore.g()
            userAgent: '', // Get from k.m().c()
            machineCode: 'android_gcm', // or 'android_gpns'
            appVersion: 32300,
            isRooted: false, // Get from q1.d()
            fingerprintBeforeTemper: null,
            isFingerprintTempered: false
        };
        
        // FCM Token - UPDATE FROM FRIDA SCRIPT
        this.fcmToken = 'YOUR_FCM_TOKEN_HERE';
    }
    
    // Generate request ID similar to Shopee's format
    generateRequestId() {
        return crypto.randomUUID().replace(/-/g, '').substring(0, 16);
    }
    
    // Create SetUserInfo protobuf-like payload
    createSetUserInfoPayload() {
        const requestId = this.generateRequestId();
        
        // This is a simplified representation - actual protobuf encoding needed
        const payload = {
            requestid: requestId,
            token: this.userInfo.token,
            country: this.userInfo.country,
            portrait: '',
            machine_code: this.deviceInfo.machineCode,
            deviceid: Buffer.from(this.deviceInfo.deviceId, 'base64'),
            pn_option: 0,
            language: this.userInfo.language,
            phone_public: false,
            pn_token: Buffer.from(this.fcmToken, 'utf8'),
            extinfo: Buffer.alloc(0),
            status: 0,
            bankacc_verified: 0,
            appversion: this.deviceInfo.appVersion,
            not_merge_extinfo: false,
            user_name: this.userInfo.username,
            cb_option: 0,
            ext: {
                deviceid: Buffer.from(this.deviceInfo.deviceId, 'base64'),
                device_fingerprint: Buffer.from(this.deviceInfo.deviceFingerprint),
                user_agent: this.deviceInfo.userAgent,
                is_rooted: this.deviceInfo.isRooted,
                fingerprint_before_temper: this.deviceInfo.fingerprintBeforeTemper,
                is_fingerprint_tempered: this.deviceInfo.isFingerprintTempered
            }
        };
        
        return { payload, requestId };
    }
    
    // Create TCP packet (simplified - needs proper protobuf encoding)
    createTCPPacket(payload) {
        // Command 67 for SetUserInfo
        const command = 67;
        
        // This is simplified - actual implementation needs protobuf encoding
        const payloadStr = JSON.stringify(payload);
        const payloadBuffer = Buffer.from(payloadStr, 'utf8');
        
        // Create packet header (simplified)
        const header = Buffer.alloc(8);
        header.writeUInt32LE(payloadBuffer.length + 4, 0); // Packet length
        header.writeUInt32LE(command, 4); // Command
        
        return Buffer.concat([header, payloadBuffer]);
    }
    
    // Send FCM token registration to server
    async sendFCMRegistration() {
        return new Promise((resolve, reject) => {
            console.log('Creating SetUserInfo payload...');
            const { payload, requestId } = this.createSetUserInfoPayload();
            
            console.log('Payload created:', {
                requestId,
                fcmToken: this.fcmToken.substring(0, 20) + '...',
                userId: this.userInfo.userId,
                deviceId: this.deviceInfo.deviceId.substring(0, 10) + '...'
            });
            
            // Create TCP packet
            const packet = this.createTCPPacket(payload);
            
            // Connect to server
            const client = net.createConnection({
                host: this.serverHost,
                port: this.serverPort
            });
            
            client.on('connect', () => {
                console.log('Connected to Shopee server');
                console.log('Sending FCM registration packet...');
                client.write(packet);
            });
            
            client.on('data', (data) => {
                console.log('Received response from server:', data.length, 'bytes');
                console.log('Response hex:', data.toString('hex'));
                client.end();
                resolve(data);
            });
            
            client.on('error', (err) => {
                console.error('Connection error:', err);
                reject(err);
            });
            
            client.on('close', () => {
                console.log('Connection closed');
            });
            
            // Timeout after 30 seconds
            setTimeout(() => {
                client.destroy();
                reject(new Error('Connection timeout'));
            }, 30000);
        });
    }
    
    // Update configuration from Frida script output
    updateFromFridaData(fridaData) {
        if (fridaData.serverHost) this.serverHost = fridaData.serverHost;
        if (fridaData.serverPort) this.serverPort = fridaData.serverPort;
        
        if (fridaData.userInfo) {
            Object.assign(this.userInfo, fridaData.userInfo);
        }
        
        if (fridaData.deviceInfo) {
            Object.assign(this.deviceInfo, fridaData.deviceInfo);
        }
        
        if (fridaData.fcmToken) {
            this.fcmToken = fridaData.fcmToken;
        }
        
        console.log('Configuration updated from Frida data');
    }
    
    // Validate configuration
    validateConfig() {
        const errors = [];
        
        if (!this.serverHost || this.serverHost === 'YOUR_SERVER_HOST') {
            errors.push('Server host not configured');
        }
        
        if (!this.fcmToken || this.fcmToken === 'YOUR_FCM_TOKEN_HERE') {
            errors.push('FCM token not configured');
        }
        
        if (!this.userInfo.userId || !this.userInfo.token) {
            errors.push('User info not configured');
        }
        
        if (!this.deviceInfo.deviceId || !this.deviceInfo.deviceFingerprint) {
            errors.push('Device info not configured');
        }
        
        return errors;
    }
}

// Example usage
async function main() {
    const sender = new ShopeeFCMSender();
    
    // Example: Update from Frida script output
    // const fridaData = JSON.parse(process.argv[2] || '{}');
    // sender.updateFromFridaData(fridaData);
    
    // Validate configuration
    const errors = sender.validateConfig();
    if (errors.length > 0) {
        console.error('Configuration errors:');
        errors.forEach(error => console.error('- ' + error));
        console.log('\nPlease run the Frida script first to capture the required data.');
        return;
    }
    
    try {
        console.log('Starting FCM registration...');
        const response = await sender.sendFCMRegistration();
        console.log('FCM registration completed successfully');
    } catch (error) {
        console.error('FCM registration failed:', error.message);
    }
}

// Export for use as module
module.exports = ShopeeFCMSender;

// Run if called directly
if (require.main === module) {
    main();
}
